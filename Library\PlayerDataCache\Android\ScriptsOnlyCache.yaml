ScriptsOnlyBuild:
  usedScripts:
    Assembly-CSharp:
    - Match3.BonusGemBonusItem
    - Match3.ColorClean
    - Match3.GameManager
    - Match3.JumpingFish
    - Match3.LargeBomb
    - Match3.LineRocket
    - Match3.ShopItemBonusItem
    - Match3.ShopItemCoin
    - Match3.ShopItemLive
    - Match3.SmallBomb
    - Match3.UIHandler
    CFXRRuntime.dll:
    - CartoonFX.CFXR_Effect
    OwnMatch3:
    - DevsDaddy.GameShield.Core.GameShieldConfig
    - OwnMatch3.Utils.DebugSettings
    OwnMatch3.dll:
    - AIGenerationConfig
    - AILevelGenerator
    - BombBonus
    - BombXBonus
    - BonusGemEffects
    - BonusGemManager
    - CameraManager
    - ColorBonus
    - ComboManager
    - CurrencyAnimationManager
    - CurrencyAttractorUI
    - CurrencyTestController
    - DevsDaddy.GameShield.Core.GameShield
    - DevsDaddy.GameShield.Demo.GameInstaller
    - EndGamePopupController
    - FPSCounter
    - FishBonus
    - FrameBuilder
    - GameProgressManager
    - GameUIDocument
    - GemAttractorUI
    - GemDestructionEffect
    - GemNew
    - GemSoundSystem
    - GetTimeNetInitializer
    - LevelLoader
    - LineRocketBonus
    - Match3.PreGamePopupController
    - Match3Board
    - ModernAILevelGeneratorWindow
    - MoveToNextScene
    - MovesWarningAnimation
    - ObjectPoolerSettings
    - ObstacleNew
    - ObstacleSoundSystem
    - OwnMatch3.AudioManager
    - OwnMatch3.GameStateManager
    - OwnMatch3.Monetization.AdConfig
    - OwnMatch3.Monetization.AdsManager
    - OwnMatch3.Shop.ShopConfiguration
    - OwnMatch3.Shop.ShopManager
    - OwnMatch3.UI.DailyRewardsManager
    - OwnMatch3.UI.EmailManager
    - OwnMatch3.UI.EmailUIController
    - OwnMatch3.UI.MenuUIController
    - OwnMatch3.UI.MissionsManager
    - OwnMatch3.UI.MissionsUIController
    - OwnMatch3.UI.Particles.SimpleParticleController
    - RocketExplosionEffect
    - RocketTrailEffect
    - ScoreManager
    - ScoreProgressBarController
    - UIManager
    - UpdateGame
    Unity.2D.Animation.Editor.dll:
    - UnityEditor.U2D.Animation.BoneGizmo
    Unity.2D.Animation.Runtime:
    - UnityEngine.U2D.Animation.SpriteLibrary
    - UnityEngine.U2D.Animation.SpriteLibraryAsset
    - UnityEngine.U2D.Animation.SpriteResolver
    - UnityEngine.U2D.Animation.SpriteSkin
    Unity.2D.Animation.Runtime.dll:
    - UnityEngine.U2D.Animation.BufferManager
    - UnityEngine.U2D.Animation.DeformationManager
    - UnityEngine.U2D.Animation.DeformationManagerUpdater
    Unity.2D.IK.Editor.dll:
    - UnityEditor.U2D.IK.IKEditorManager
    Unity.2D.IK.Runtime:
    - UnityEngine.U2D.IK.IKManager2D
    - UnityEngine.U2D.IK.LimbSolver2D
    Unity.2D.SpriteShape.Runtime.dll:
    - UnityEngine.U2D.SpriteShape
    - UnityEngine.U2D.SpriteShapeController
    - UnityEngine.U2D.SpriteShapeDefaultCreator
    Unity.2D.Tilemap.Editor.dll:
    - UnityEditor.Tilemaps.GridBrushPickStore
    - UnityEditor.Tilemaps.GridPaintPaletteWindow
    - UnityEditor.Tilemaps.GridPaintingState
    - UnityEditor.Tilemaps.GridPaletteBrushes
    - UnityEditor.Tilemaps.GridPalettes
    - UnityEditor.Tilemaps.PaintableSceneViewGrid
    - UnityEditor.Tilemaps.SceneViewGridManager
    - UnityEditor.Tilemaps.SceneViewOpenTilePaletteHelper
    - UnityEditor.Tilemaps.TileDragAndDropManager
    - UnityEditor.Tilemaps.TilemapEditorToolPreferences
    - UnityEditor.Tilemaps.TilemapPrefabStageHelper
    Unity.2D.Tilemap.Extras.dll:
    - UnityEngine.RuleTile
    Unity.AI.Animate.dll:
    - Unity.AI.Animate.Services.SessionPersistence.AnimateGeneratorSettings
    - Unity.AI.Animate.Services.SessionPersistence.ObjectPersistence
    Unity.AI.Generators.UI.dll:
    - Unity.AI.Generators.UI.Utilities.DragAndDropCache
    Unity.AI.Image.dll:
    - Unity.AI.Image.Services.SessionPersistence.ObjectPersistence
    - Unity.AI.Image.Services.SessionPersistence.TextureGeneratorSettings
    - Unity.AI.Image.Windows.TextureGeneratorWindow
    Unity.AI.Material.dll:
    - Unity.AI.Material.Services.SessionPersistence.MaterialGeneratorSettings
    - Unity.AI.Material.Services.SessionPersistence.ObjectPersistence
    Unity.AI.Sound.dll:
    - Unity.AI.Sound.Services.SessionPersistence.ObjectPersistence
    - Unity.AI.Sound.Services.SessionPersistence.SoundGeneratorSettings
    Unity.AI.Toolkit.Accounts.dll:
    - Unity.AI.Toolkit.Accounts.Services.Core.AccountPersistence
    Unity.AppUI.dll:
    - Unity.AppUI.Core.AppUISettings
    Unity.InputSystem:
    - UnityEngine.InputSystem.InputActionAsset
    - UnityEngine.InputSystem.InputActionReference
    - UnityEngine.InputSystem.UI.InputSystemUIInputModule
    Unity.InputSystem.dll:
    - UnityEngine.InputSystem.InputActionAsset
    - UnityEngine.InputSystem.InputActionReference
    - UnityEngine.InputSystem.InputSettings
    - UnityEngine.InputSystem.InputSystemObject
    - UnityEngine.InputSystem.RemoteInputPlayerConnection
    - UnityEngine.InputSystem.UI.InputSystemUIInputModule
    Unity.LevelPlay:
    - IronSourceMediationSettings
    Unity.LevelPlay.Editor:
    - IronSourceMediatedNetworkSettings
    Unity.LevelPlay.Editor.dll:
    - IronSourceMediatedNetworkSettings
    - IronSourceMediationSettingsInspector
    Unity.LevelPlay.dll:
    - IronSourceMediationSettings
    Unity.Mobile.AndroidLogcat.Editor.dll:
    - Unity.Android.Logcat.AndroidLogcatConsoleWindow
    - Unity.Android.Logcat.AndroidLogcatManager
    Unity.Recorder.Editor.dll:
    - UnityEditor.Recorder.RecorderPreferencesSettings
    Unity.RenderPipelines.Core.Runtime.dll:
    - UnityEngine.Rendering.ProbeVolumesOptions
    - UnityEngine.Rendering.UI.DebugUIHandlerBitField
    - UnityEngine.Rendering.UI.DebugUIHandlerButton
    - UnityEngine.Rendering.UI.DebugUIHandlerCanvas
    - UnityEngine.Rendering.UI.DebugUIHandlerColor
    - UnityEngine.Rendering.UI.DebugUIHandlerContainer
    - UnityEngine.Rendering.UI.DebugUIHandlerEnumField
    - UnityEngine.Rendering.UI.DebugUIHandlerEnumHistory
    - UnityEngine.Rendering.UI.DebugUIHandlerFloatField
    - UnityEngine.Rendering.UI.DebugUIHandlerFoldout
    - UnityEngine.Rendering.UI.DebugUIHandlerGroup
    - UnityEngine.Rendering.UI.DebugUIHandlerHBox
    - UnityEngine.Rendering.UI.DebugUIHandlerIndirectFloatField
    - UnityEngine.Rendering.UI.DebugUIHandlerIndirectToggle
    - UnityEngine.Rendering.UI.DebugUIHandlerIntField
    - UnityEngine.Rendering.UI.DebugUIHandlerMessageBox
    - UnityEngine.Rendering.UI.DebugUIHandlerObject
    - UnityEngine.Rendering.UI.DebugUIHandlerObjectList
    - UnityEngine.Rendering.UI.DebugUIHandlerObjectPopupField
    - UnityEngine.Rendering.UI.DebugUIHandlerPanel
    - UnityEngine.Rendering.UI.DebugUIHandlerPersistentCanvas
    - UnityEngine.Rendering.UI.DebugUIHandlerProgressBar
    - UnityEngine.Rendering.UI.DebugUIHandlerRenderingLayerField
    - UnityEngine.Rendering.UI.DebugUIHandlerRow
    - UnityEngine.Rendering.UI.DebugUIHandlerToggle
    - UnityEngine.Rendering.UI.DebugUIHandlerToggleHistory
    - UnityEngine.Rendering.UI.DebugUIHandlerUIntField
    - UnityEngine.Rendering.UI.DebugUIHandlerVBox
    - UnityEngine.Rendering.UI.DebugUIHandlerValue
    - UnityEngine.Rendering.UI.DebugUIHandlerValueTuple
    - UnityEngine.Rendering.UI.DebugUIHandlerVector2
    - UnityEngine.Rendering.UI.DebugUIHandlerVector3
    - UnityEngine.Rendering.UI.DebugUIHandlerVector4
    - UnityEngine.Rendering.UI.UIFoldout
    - UnityEngine.Rendering.VolumeProfile
    Unity.RenderPipelines.Universal.2D.Runtime:
    - UnityEngine.Rendering.Universal.Light2D
    Unity.RenderPipelines.Universal.2D.Runtime.dll:
    - UnityEngine.Rendering.Universal.Light2D
    - UnityEngine.Rendering.Universal.Renderer2DData
    Unity.RenderPipelines.Universal.Editor.dll:
    - UnityEditor.Rendering.Universal.UniversalProjectSettings
    Unity.RenderPipelines.Universal.Runtime:
    - UnityEngine.Rendering.Universal.UniversalAdditionalCameraData
    Unity.RenderPipelines.Universal.Runtime.dll:
    - UnityEngine.Rendering.Universal.Bloom
    - UnityEngine.Rendering.Universal.ChannelMixer
    - UnityEngine.Rendering.Universal.ChromaticAberration
    - UnityEngine.Rendering.Universal.ColorAdjustments
    - UnityEngine.Rendering.Universal.ColorCurves
    - UnityEngine.Rendering.Universal.ColorLookup
    - UnityEngine.Rendering.Universal.DepthOfField
    - UnityEngine.Rendering.Universal.FilmGrain
    - UnityEngine.Rendering.Universal.LensDistortion
    - UnityEngine.Rendering.Universal.LiftGammaGain
    - UnityEngine.Rendering.Universal.MotionBlur
    - UnityEngine.Rendering.Universal.PaniniProjection
    - UnityEngine.Rendering.Universal.PostProcessData
    - UnityEngine.Rendering.Universal.ScreenSpaceLensFlare
    - UnityEngine.Rendering.Universal.ShadowsMidtonesHighlights
    - UnityEngine.Rendering.Universal.SplitToning
    - UnityEngine.Rendering.Universal.Tonemapping
    - UnityEngine.Rendering.Universal.UniversalAdditionalCameraData
    - UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset
    - UnityEngine.Rendering.Universal.UniversalRenderPipelineGlobalSettings
    - UnityEngine.Rendering.Universal.Vignette
    - UnityEngine.Rendering.Universal.WhiteBalance
    Unity.Rider.Editor.dll:
    - Packages.Rider.Editor.UnitTesting.CallbackData
    Unity.ShaderGraph.Editor.dll:
    - UnityEditor.ShaderGraph.Drawing.MaterialGraphEditWindow
    Unity.TextMeshPro.dll:
    - TMPro.TMP_FontAsset
    Unity.VisualEffectGraph.Editor.dll:
    - UnityEditor.VFX.UI.VFXViewWindow
    Unity.VisualEffectGraph.Runtime.dll:
    - UnityEngine.VFX.VFXRuntimeResources
    UnityEditor.Graphs.dll:
    - UnityEditor.Graphs.AnimationBlendTree.Graph
    - UnityEditor.Graphs.AnimationBlendTree.GraphGUI
    - UnityEditor.Graphs.AnimationStateMachine.Graph
    - UnityEditor.Graphs.AnimationStateMachine.GraphGUI
    - UnityEditor.Graphs.AnimatorControllerTool
    UnityEditor.TestRunner.dll:
    - UnityEditor.TestTools.TestRunner.Api.CallbacksHolder
    - UnityEditor.TestTools.TestRunner.CommandLineTest.RunData
    - UnityEditor.TestTools.TestRunner.TestListCacheData
    - UnityEditor.TestTools.TestRunner.TestRun.TestJobDataHolder
    UnityEngine.UI:
    - UnityEngine.EventSystems.EventSystem
    UnityEngine.UI.dll:
    - UnityEngine.EventSystems.EventSystem
    - UnityEngine.EventSystems.EventTrigger
    - UnityEngine.UI.Button
    - UnityEngine.UI.CanvasScaler
    - UnityEngine.UI.ContentSizeFitter
    - UnityEngine.UI.GraphicRaycaster
    - UnityEngine.UI.HorizontalLayoutGroup
    - UnityEngine.UI.Image
    - UnityEngine.UI.LayoutElement
    - UnityEngine.UI.Mask
    - UnityEngine.UI.ScrollRect
    - UnityEngine.UI.Scrollbar
    - UnityEngine.UI.Text
    - UnityEngine.UI.Toggle
    - UnityEngine.UI.VerticalLayoutGroup
  serializedClasses:
    Assembly-CSharp:
    - Match3.BonusSetting
    - Match3.GameManager/BonusItemEntry
    - Match3.GameSettings
    - Match3.MatchShape
    - Match3.ShopSetting
    - Match3.SoundSetting
    - Match3.VisualSetting
    CFXRRuntime:
    - CartoonFX.CFXR_Effect/AnimatedLight
    - CartoonFX.CFXR_Effect/CameraShake
    OwnMatch3:
    - BonusGemData
    - ComboWordStyle
    - DevsDaddy.GameShield.Core.DeveloperContacts
    - DifficultyRange
    - GemAttractorUI/GemSpriteMapping
    - GemTileData
    - LevelDimensions
    - MatchShape
    - MovesCalculationSettings
    - ObstacleSoundData
    - ObstacleTileData
    - OwnMatch3.Shop.BoosterShopItem
    - OwnMatch3.UI.DailyRewardsManager/DailyRewardData
    - PatternWeights
    - PhantomExtra.CurvedProgressBar/UxmlSerializedData
    - PhantomExtra.StarRatingElement/UxmlSerializedData
    - ShapeSelectionSettings
    - ShapeWeights
    - StageProgressBar/UxmlSerializedData
    Unity.2D.Animation.Runtime:
    - UnityEngine.U2D.Animation.SpriteCategoryEntry
    - UnityEngine.U2D.Animation.SpriteLibCategory
    Unity.2D.IK.Runtime:
    - UnityEngine.U2D.IK.IKChain2D
    - UnityEngine.U2D.IK.IKManager2D/SolverEditorData
    Unity.2D.SpriteShape.Runtime:
    - UnityEngine.U2D.AngleRange
    - UnityEngine.U2D.CornerSprite
    - UnityEngine.U2D.Spline
    - UnityEngine.U2D.SplineControlPoint
    Unity.InputSystem:
    - UnityEngine.InputSystem.InputAction
    - UnityEngine.InputSystem.InputActionMap
    - UnityEngine.InputSystem.InputBinding
    - UnityEngine.InputSystem.InputControlScheme
    - UnityEngine.InputSystem.InputControlScheme/DeviceRequirement
    Unity.RenderPipelines.Core.Runtime:
    - UnityEngine.Rendering.APVLeakReductionModeParameter
    - UnityEngine.Rendering.BoolParameter
    - UnityEngine.Rendering.ClampedFloatParameter
    - UnityEngine.Rendering.ClampedIntParameter
    - UnityEngine.Rendering.ColorParameter
    - UnityEngine.Rendering.FloatParameter
    - UnityEngine.Rendering.IncludeAdditionalRPAssets
    - UnityEngine.Rendering.LightmapSamplingSettings
    - UnityEngine.Rendering.MinFloatParameter
    - UnityEngine.Rendering.NoInterpTextureParameter
    - UnityEngine.Rendering.ProbeVolumeBakingResources
    - UnityEngine.Rendering.ProbeVolumeBlendingTextureMemoryBudget
    - UnityEngine.Rendering.ProbeVolumeDebugResources
    - UnityEngine.Rendering.ProbeVolumeGlobalSettings
    - UnityEngine.Rendering.ProbeVolumeRuntimeResources
    - UnityEngine.Rendering.ProbeVolumeSHBands
    - UnityEngine.Rendering.ProbeVolumeSceneData
    - UnityEngine.Rendering.ProbeVolumeTextureMemoryBudget
    - UnityEngine.Rendering.RenderGraphGlobalSettings
    - UnityEngine.Rendering.RenderGraphModule.Util.RenderGraphUtilsResources
    - UnityEngine.Rendering.RenderPipelineGraphicsSettingsContainer
    - UnityEngine.Rendering.RenderingDebuggerRuntimeResources
    - UnityEngine.Rendering.STP/RuntimeResources
    - UnityEngine.Rendering.SerializedDictionary`2
    - UnityEngine.Rendering.ShaderStrippingSetting
    - UnityEngine.Rendering.TextureCurve
    - UnityEngine.Rendering.TextureCurveParameter
    - UnityEngine.Rendering.TextureParameter
    - UnityEngine.Rendering.Vector2Parameter
    - UnityEngine.Rendering.Vector3Parameter
    - UnityEngine.Rendering.Vector4Parameter
    - UnityEngine.Rendering.VrsRenderPipelineRuntimeResources
    Unity.RenderPipelines.GPUDriven.Runtime:
    - UnityEngine.Rendering.GPUResidentDrawerResources
    Unity.RenderPipelines.Universal.2D.Runtime:
    - UnityEngine.Rendering.Universal.Light2DBlendStyle
    Unity.RenderPipelines.Universal.Runtime:
    - UnityEngine.Rendering.Universal.DepthOfFieldModeParameter
    - UnityEngine.Rendering.Universal.DownscaleParameter
    - UnityEngine.Rendering.Universal.FilmGrainLookupParameter
    - UnityEngine.Rendering.Universal.HDRACESPresetParameter
    - UnityEngine.Rendering.Universal.MotionBlurModeParameter
    - UnityEngine.Rendering.Universal.MotionBlurQualityParameter
    - UnityEngine.Rendering.Universal.NeutralRangeReductionModeParameter
    - UnityEngine.Rendering.Universal.PostProcessData/ShaderResources
    - UnityEngine.Rendering.Universal.PostProcessData/TextureResources
    - UnityEngine.Rendering.Universal.RenderGraphSettings
    - UnityEngine.Rendering.Universal.Renderer2DResources
    - UnityEngine.Rendering.Universal.ScreenSpaceAmbientOcclusionDynamicResources
    - UnityEngine.Rendering.Universal.ScreenSpaceAmbientOcclusionPersistentResources
    - UnityEngine.Rendering.Universal.ScreenSpaceLensFlareResolutionParameter
    - UnityEngine.Rendering.Universal.ScriptableRendererData/DebugShaderResources
    - UnityEngine.Rendering.Universal.ScriptableRendererData/ProbeVolumeResources
    - UnityEngine.Rendering.Universal.TemporalAA/Settings
    - UnityEngine.Rendering.Universal.TonemappingModeParameter
    - UnityEngine.Rendering.Universal.URPDefaultVolumeProfileSettings
    - UnityEngine.Rendering.Universal.URPShaderStrippingSetting
    - UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset/TextureResources
    - UnityEngine.Rendering.Universal.UniversalRenderPipelineDebugShaders
    - UnityEngine.Rendering.Universal.UniversalRenderPipelineEditorAssets
    - UnityEngine.Rendering.Universal.UniversalRenderPipelineEditorMaterials
    - UnityEngine.Rendering.Universal.UniversalRenderPipelineEditorShaders
    - UnityEngine.Rendering.Universal.UniversalRenderPipelineRuntimeShaders
    - UnityEngine.Rendering.Universal.UniversalRenderPipelineRuntimeTextures
    - UnityEngine.Rendering.Universal.UniversalRenderPipelineRuntimeXRResources
    - UnityEngine.Rendering.Universal.UniversalRendererResources
    UnityEngine.CoreModule:
    - UnityEngine.Bounds
    - UnityEngine.Rendering.RenderPipelineGraphicsSettingsCollection
    UnityEngine.TextCoreFontEngineModule:
    - UnityEngine.TextCore.FaceInfo
    - UnityEngine.TextCore.Glyph
    - UnityEngine.TextCore.GlyphMetrics
    - UnityEngine.TextCore.GlyphRect
    - UnityEngine.TextCore.LowLevel.GlyphAdjustmentRecord
    - UnityEngine.TextCore.LowLevel.GlyphPairAdjustmentRecord
    - UnityEngine.TextCore.LowLevel.GlyphValueRecord
    UnityEngine.TextCoreTextEngineModule:
    - UnityEngine.TextCore.Text.Character
    - UnityEngine.TextCore.Text.FontAssetCreationEditorSettings
    - UnityEngine.TextCore.Text.FontFeatureTable
    - UnityEngine.TextCore.Text.FontWeightPair
    UnityEngine.UIElementsModule:
    - UnityEngine.UIElements.Button/UxmlFactory
    - UnityEngine.UIElements.Button/UxmlSerializedData
    - UnityEngine.UIElements.DynamicAtlasSettings
    - UnityEngine.UIElements.Foldout/UxmlFactory
    - UnityEngine.UIElements.Foldout/UxmlSerializedData
    - UnityEngine.UIElements.Image/UxmlFactory
    - UnityEngine.UIElements.Image/UxmlSerializedData
    - UnityEngine.UIElements.Label/UxmlFactory
    - UnityEngine.UIElements.Label/UxmlSerializedData
    - UnityEngine.UIElements.ListView/UxmlFactory
    - UnityEngine.UIElements.ListView/UxmlSerializedData
    - UnityEngine.UIElements.ScrollView/UxmlFactory
    - UnityEngine.UIElements.ScrollView/UxmlSerializedData
    - UnityEngine.UIElements.Slider/UxmlFactory
    - UnityEngine.UIElements.Slider/UxmlSerializedData
    - UnityEngine.UIElements.StyleComplexSelector
    - UnityEngine.UIElements.StyleProperty
    - UnityEngine.UIElements.StyleRule
    - UnityEngine.UIElements.StyleSelector
    - UnityEngine.UIElements.StyleSelectorPart
    - UnityEngine.UIElements.StyleSheet/ImportStruct
    - UnityEngine.UIElements.StyleSheets.Dimension
    - UnityEngine.UIElements.StyleSheets.ScalableImage
    - UnityEngine.UIElements.StyleValueHandle
    - UnityEngine.UIElements.TemplateAsset
    - UnityEngine.UIElements.TemplateContainer/UxmlSerializedData
    - UnityEngine.UIElements.UxmlNamespaceDefinition
    - UnityEngine.UIElements.UxmlRootElementFactory
    - UnityEngine.UIElements.VisualElement/UxmlFactory
    - UnityEngine.UIElements.VisualElement/UxmlSerializedData
    - UnityEngine.UIElements.VisualElementAsset
    - UnityEngine.UIElements.VisualTreeAsset/AssetEntry
    - UnityEngine.UIElements.VisualTreeAsset/UsingEntry
  methodsToPreserve: []
  sceneClasses:
    Assets/OwnMatch3/DecreaseScene.unity:
    - Class: 1
      Script: {instanceID: 0}
    - Class: 4
      Script: {instanceID: 0}
    - Class: 20
      Script: {instanceID: 0}
    - Class: 21
      Script: {instanceID: 0}
    - Class: 28
      Script: {instanceID: 0}
    - Class: 48
      Script: {instanceID: 0}
    - Class: 49
      Script: {instanceID: 0}
    - Class: 72
      Script: {instanceID: 0}
    - Class: 74
      Script: {instanceID: 0}
    - Class: 82
      Script: {instanceID: 0}
    - Class: 83
      Script: {instanceID: 0}
    - Class: 84
      Script: {instanceID: 0}
    - Class: 89
      Script: {instanceID: 0}
    - Class: 91
      Script: {instanceID: 0}
    - Class: 95
      Script: {instanceID: 0}
    - Class: 104
      Script: {instanceID: 0}
    - Class: 114
      Script: {instanceID: 1272}
    - Class: 114
      Script: {instanceID: 1274}
    - Class: 114
      Script: {instanceID: 1276}
    - Class: 114
      Script: {instanceID: 1288}
    - Class: 114
      Script: {instanceID: 1298}
    - Class: 114
      Script: {instanceID: 1300}
    - Class: 114
      Script: {instanceID: 40262}
    - Class: 114
      Script: {instanceID: 40876}
    - Class: 114
      Script: {instanceID: 41664}
    - Class: 114
      Script: {instanceID: 41738}
    - Class: 114
      Script: {instanceID: 42160}
    - Class: 114
      Script: {instanceID: 42988}
    - Class: 114
      Script: {instanceID: 43046}
    - Class: 114
      Script: {instanceID: 46572}
    - Class: 114
      Script: {instanceID: 46614}
    - Class: 114
      Script: {instanceID: 48264}
    - Class: 114
      Script: {instanceID: 50108}
    - Class: 114
      Script: {instanceID: 50204}
    - Class: 114
      Script: {instanceID: 51830}
    - Class: 114
      Script: {instanceID: 51876}
    - Class: 114
      Script: {instanceID: 52554}
    - Class: 114
      Script: {instanceID: 52984}
    - Class: 114
      Script: {instanceID: 53056}
    - Class: 114
      Script: {instanceID: 54220}
    - Class: 114
      Script: {instanceID: 54990}
    - Class: 114
      Script: {instanceID: 55824}
    - Class: 114
      Script: {instanceID: 55840}
    - Class: 114
      Script: {instanceID: 55844}
    - Class: 114
      Script: {instanceID: 56108}
    - Class: 114
      Script: {instanceID: 56234}
    - Class: 114
      Script: {instanceID: 56800}
    - Class: 114
      Script: {instanceID: 57194}
    - Class: 114
      Script: {instanceID: 57320}
    - Class: 114
      Script: {instanceID: 57690}
    - Class: 115
      Script: {instanceID: 0}
    - Class: 128
      Script: {instanceID: 0}
    - Class: 157
      Script: {instanceID: 0}
    - Class: 196
      Script: {instanceID: 0}
    - Class: 210
      Script: {instanceID: 0}
    - Class: 212
      Script: {instanceID: 0}
    - Class: 213
      Script: {instanceID: 0}
    - Class: 241
      Script: {instanceID: 0}
    - Class: 243
      Script: {instanceID: 0}
    - Class: 245
      Script: {instanceID: 0}
    - Class: 331
      Script: {instanceID: 0}
    - Class: 73398921
      Script: {instanceID: 0}
    - Class: 687078895
      Script: {instanceID: 0}
    - Class: 1818360608
      Script: {instanceID: 0}
    - Class: 2058629509
      Script: {instanceID: 0}
    - Class: 2083052967
      Script: {instanceID: 0}
    Assets/OwnMatch3/Scripts/OwnMatch.unity:
    - Class: 1
      Script: {instanceID: 0}
    - Class: 4
      Script: {instanceID: 0}
    - Class: 20
      Script: {instanceID: 0}
    - Class: 21
      Script: {instanceID: 0}
    - Class: 28
      Script: {instanceID: 0}
    - Class: 43
      Script: {instanceID: 0}
    - Class: 48
      Script: {instanceID: 0}
    - Class: 74
      Script: {instanceID: 0}
    - Class: 81
      Script: {instanceID: 0}
    - Class: 82
      Script: {instanceID: 0}
    - Class: 83
      Script: {instanceID: 0}
    - Class: 84
      Script: {instanceID: 0}
    - Class: 89
      Script: {instanceID: 0}
    - Class: 91
      Script: {instanceID: 0}
    - Class: 95
      Script: {instanceID: 0}
    - Class: 104
      Script: {instanceID: 0}
    - Class: 108
      Script: {instanceID: 0}
    - Class: 114
      Script: {instanceID: 1272}
    - Class: 114
      Script: {instanceID: 1274}
    - Class: 114
      Script: {instanceID: 1276}
    - Class: 114
      Script: {instanceID: 1282}
    - Class: 114
      Script: {instanceID: 1288}
    - Class: 114
      Script: {instanceID: 1298}
    - Class: 114
      Script: {instanceID: 1300}
    - Class: 114
      Script: {instanceID: 18982}
    - Class: 114
      Script: {instanceID: 18986}
    - Class: 114
      Script: {instanceID: 18994}
    - Class: 114
      Script: {instanceID: 18996}
    - Class: 114
      Script: {instanceID: 18998}
    - Class: 114
      Script: {instanceID: 19000}
    - Class: 114
      Script: {instanceID: 19002}
    - Class: 114
      Script: {instanceID: 19008}
    - Class: 114
      Script: {instanceID: 19012}
    - Class: 114
      Script: {instanceID: 19014}
    - Class: 114
      Script: {instanceID: 19018}
    - Class: 114
      Script: {instanceID: 19022}
    - Class: 114
      Script: {instanceID: 19028}
    - Class: 114
      Script: {instanceID: 19030}
    - Class: 114
      Script: {instanceID: 19036}
    - Class: 114
      Script: {instanceID: 19038}
    - Class: 114
      Script: {instanceID: 19040}
    - Class: 114
      Script: {instanceID: 19052}
    - Class: 114
      Script: {instanceID: 19070}
    - Class: 114
      Script: {instanceID: 19074}
    - Class: 114
      Script: {instanceID: 19076}
    - Class: 114
      Script: {instanceID: 19078}
    - Class: 114
      Script: {instanceID: 19080}
    - Class: 114
      Script: {instanceID: 39860}
    - Class: 114
      Script: {instanceID: 40634}
    - Class: 114
      Script: {instanceID: 40876}
    - Class: 114
      Script: {instanceID: 41634}
    - Class: 114
      Script: {instanceID: 42384}
    - Class: 114
      Script: {instanceID: 42718}
    - Class: 114
      Script: {instanceID: 42988}
    - Class: 114
      Script: {instanceID: 44180}
    - Class: 114
      Script: {instanceID: 45056}
    - Class: 114
      Script: {instanceID: 45176}
    - Class: 114
      Script: {instanceID: 45530}
    - Class: 114
      Script: {instanceID: 45558}
    - Class: 114
      Script: {instanceID: 46572}
    - Class: 114
      Script: {instanceID: 48154}
    - Class: 114
      Script: {instanceID: 48264}
    - Class: 114
      Script: {instanceID: 48556}
    - Class: 114
      Script: {instanceID: 48624}
    - Class: 114
      Script: {instanceID: 48688}
    - Class: 114
      Script: {instanceID: 49956}
    - Class: 114
      Script: {instanceID: 50304}
    - Class: 114
      Script: {instanceID: 51346}
    - Class: 114
      Script: {instanceID: 51662}
    - Class: 114
      Script: {instanceID: 51796}
    - Class: 114
      Script: {instanceID: 52418}
    - Class: 114
      Script: {instanceID: 52538}
    - Class: 114
      Script: {instanceID: 53934}
    - Class: 114
      Script: {instanceID: 54168}
    - Class: 114
      Script: {instanceID: 54422}
    - Class: 114
      Script: {instanceID: 54892}
    - Class: 114
      Script: {instanceID: 55314}
    - Class: 114
      Script: {instanceID: 55666}
    - Class: 114
      Script: {instanceID: 56024}
    - Class: 114
      Script: {instanceID: 56120}
    - Class: 114
      Script: {instanceID: 56222}
    - Class: 114
      Script: {instanceID: 56700}
    - Class: 114
      Script: {instanceID: 57194}
    - Class: 114
      Script: {instanceID: 57320}
    - Class: 114
      Script: {instanceID: 59712}
    - Class: 115
      Script: {instanceID: 0}
    - Class: 120
      Script: {instanceID: 0}
    - Class: 128
      Script: {instanceID: 0}
    - Class: 157
      Script: {instanceID: 0}
    - Class: 196
      Script: {instanceID: 0}
    - Class: 198
      Script: {instanceID: 0}
    - Class: 199
      Script: {instanceID: 0}
    - Class: 212
      Script: {instanceID: 0}
    - Class: 213
      Script: {instanceID: 0}
    - Class: 241
      Script: {instanceID: 0}
    - Class: 243
      Script: {instanceID: 0}
    - Class: 245
      Script: {instanceID: 0}
    - Class: 156049354
      Script: {instanceID: 0}
    - Class: 483693784
      Script: {instanceID: 0}
    - Class: 687078895
      Script: {instanceID: 0}
    - Class: 1839735485
      Script: {instanceID: 0}
    - Class: 1971053207
      Script: {instanceID: 0}
  scriptHashData:
  - hash:
      serializedVersion: 2
      Hash: ea78afb1b30663b7c04c956e9f2acdec
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: UniversalRenderPipelineAsset
  - hash:
      serializedVersion: 2
      Hash: 25014936a75b3aca2b626b9f3b0ab673
    assemblyName: OwnMatch3
    namespaceName: OwnMatch3.Monetization
    className: AdsManager
  - hash:
      serializedVersion: 2
      Hash: 2ee940552eb81ff7512d5e5d18441bbc
    assemblyName: OwnMatch3
    namespaceName: DevsDaddy.GameShield.Demo.Controllers
    className: PlayerController
  - hash:
      serializedVersion: 2
      Hash: 45f7ca0927fa8e438244c289e0f8060e
    assemblyName: OwnMatch3
    namespaceName: 
    className: GemDestructionEffect
  - hash:
      serializedVersion: 2
      Hash: 6cbf8cb4d3bcd59e9505f241a6a9c7e6
    assemblyName: OwnMatch3
    namespaceName: 
    className: AILevelIntegration
  - hash:
      serializedVersion: 2
      Hash: 94bb854379225d83b49d544aaaf3c0fa
    assemblyName: OwnMatch3
    namespaceName: 
    className: AILevelGeneratorIntegration
  - hash:
      serializedVersion: 2
      Hash: 94994effe312e1538a9a6591e14ba363
    assemblyName: OwnMatch3
    namespaceName: 
    className: BombXBonus
  - hash:
      serializedVersion: 2
      Hash: 22687dcc134fe00915b782578742c445
    assemblyName: OwnMatch3
    namespaceName: 
    className: ColorBonus
  - hash:
      serializedVersion: 2
      Hash: 5c3600825d24e3291caec3d23fc96a51
    assemblyName: OwnMatch3
    namespaceName: 
    className: BombBonus
  - hash:
      serializedVersion: 2
      Hash: 5782d9abe734b4c1d4fa624279e3fd6f
    assemblyName: OwnMatch3
    namespaceName: 
    className: FishBonus
  - hash:
      serializedVersion: 2
      Hash: d5af81bc0423ee4eeae435f217b3fdbf
    assemblyName: OwnMatch3
    namespaceName: 
    className: LineRocketBonus
  - hash:
      serializedVersion: 2
      Hash: fffb7065ae131dedcc1940fa280720a8
    assemblyName: OwnMatch3
    namespaceName: 
    className: ModernAILevelGeneratorWindow
  - hash:
      serializedVersion: 2
      Hash: 2fdccfd3d02210fadcf165f4c98bb1c4
    assemblyName: OwnMatch3
    namespaceName: 
    className: GemNew
  - hash:
      serializedVersion: 2
      Hash: 5e24afaff70382fa4120726763a94a3a
    assemblyName: OwnMatch3
    namespaceName: 
    className: LevelCompletionHandler
  - hash:
      serializedVersion: 2
      Hash: 6aefb1fe043c6f405ffea96f32fe08af
    assemblyName: OwnMatch3
    namespaceName: 
    className: LevelLoader
  - hash:
      serializedVersion: 2
      Hash: 5eeb8d04db298ba14d1c037a60de1559
    assemblyName: OwnMatch3
    namespaceName: 
    className: ObstacleNew
  - hash:
      serializedVersion: 2
      Hash: cde6ebfaa7d3d48bd34ed8f65db1af68
    assemblyName: OwnMatch3
    namespaceName: 
    className: SaveSystemIntegration
  - hash:
      serializedVersion: 2
      Hash: 72c372344cd7bd9325d3df4c9b713435
    assemblyName: OwnMatch3
    namespaceName: 
    className: Match3Board
  - hash:
      serializedVersion: 2
      Hash: 0a279f7853abcc21073a3bf1a8e4719b
    assemblyName: OwnMatch3
    namespaceName: 
    className: ComboManager
  - hash:
      serializedVersion: 2
      Hash: b128e78dcaa73e78fd2ecdd997dc7c67
    assemblyName: OwnMatch3
    namespaceName: 
    className: PreGamePopupSetup
  - hash:
      serializedVersion: 2
      Hash: 16670f0d23efba0bde8ae93fd3655830
    assemblyName: OwnMatch3
    namespaceName: 
    className: MovesWarningAnimation
  - hash:
      serializedVersion: 2
      Hash: 507fbfb79c6a44b56e21c94e3d4becd2
    assemblyName: OwnMatch3
    namespaceName: 
    className: GemAttractorUI
  - hash:
      serializedVersion: 2
      Hash: 916d5be75a2208e33df749d8c8a880b1
    assemblyName: OwnMatch3
    namespaceName: 
    className: StageProgressBarDebug
  - hash:
      serializedVersion: 2
      Hash: 7224c59b1dec673bd6561cffd598f0a6
    assemblyName: OwnMatch3
    namespaceName: 
    className: CameraSetupHelper
  - hash:
      serializedVersion: 2
      Hash: d7d2c0e36b621c798544b4578e25bd1c
    assemblyName: OwnMatch3
    namespaceName: 
    className: GameProgressManager
  - hash:
      serializedVersion: 2
      Hash: ce9730549744364b3e2b4c6dbfde923f
    assemblyName: OwnMatch3
    namespaceName: 
    className: CameraManager
  - hash:
      serializedVersion: 2
      Hash: 6eaf1a1d3876a888c542f16999b641aa
    assemblyName: OwnMatch3
    namespaceName: OwnMatch3.UI
    className: MenuUIController
  - hash:
      serializedVersion: 2
      Hash: cb8b7a5f7c8557cb1dad338319a78318
    assemblyName: OwnMatch3
    namespaceName: OwnMatch3.Editor
    className: SimpleCameraManagerEditor
  - hash:
      serializedVersion: 2
      Hash: e19816e19d3cad8a32babf36a940cf9d
    assemblyName: OwnMatch3
    namespaceName: DevsDaddy.GameShield.Core.Editor.Tools
    className: UltimateSecuredTypeConverter
  - hash:
      serializedVersion: 2
      Hash: 994af9eab20990a710e4cc46db83b2e2
    assemblyName: OwnMatch3
    namespaceName: OwnMatch3.Editor
    className: DebugMigrationWindow
  - hash:
      serializedVersion: 2
      Hash: 0f0ecdeb16f7fb57b6c5ebc0fe1bc7a2
    assemblyName: OwnMatch3
    namespaceName: DevsDaddy.GameShield.Core.Editor.Tools
    className: SmartSecuredTypeConverter
  - hash:
      serializedVersion: 2
      Hash: 3598ce2654f5220c98846702f0994c6d
    assemblyName: OwnMatch3
    namespaceName: OwnMatch3.UI
    className: EmailUIController
  - hash:
      serializedVersion: 2
      Hash: fe5deeea6f02140e87b5443fed7fd05b
    assemblyName: OwnMatch3
    namespaceName: DevsDaddy.GameShield.Core.Editor.Tools
    className: SecuredTypeSettingsWindow
  - hash:
      serializedVersion: 2
      Hash: dbb39cd5e31e2d474217ef69a0fb3dc4
    assemblyName: OwnMatch3
    namespaceName: DevsDaddy.GameShield.Core.Editor
    className: GameShieldWizzard
  - hash:
      serializedVersion: 2
      Hash: 78ce701c1d605eca3177040d733fccdb
    assemblyName: OwnMatch3
    namespaceName: OwnMatch3.Monetization
    className: AdConfig
  - hash:
      serializedVersion: 2
      Hash: 6f37e292425e481a6cbb4584286e29e4
    assemblyName: OwnMatch3
    namespaceName: 
    className: BonusGemEffects
  - hash:
      serializedVersion: 2
      Hash: 07c8b038e078d18bddcf11b747c5fa0b
    assemblyName: OwnMatch3
    namespaceName: OwnMatch3.UI
    className: MissionsUIController
  - hash:
      serializedVersion: 2
      Hash: 4c8205bc1ee9b141bb343e58adaa8dfd
    assemblyName: OwnMatch3
    namespaceName: OwnMatch3
    className: GameStateManager
  - hash:
      serializedVersion: 2
      Hash: be8112bff7471f21828694cf3e54997b
    assemblyName: OwnMatch3
    namespaceName: DevsDaddy.GameShield.Core
    className: GameShield
  - hash:
      serializedVersion: 2
      Hash: 1a111062029d6c53f1913c4283b096b7
    assemblyName: OwnMatch3
    namespaceName: 
    className: SaveSystemTester
  - hash:
      serializedVersion: 2
      Hash: 95c8a8c68e664d4868755f06e7c55997
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: DepthOfField
  - hash:
      serializedVersion: 2
      Hash: 719b867ed39d17b25e9d69fbca5bde4a
    assemblyName: OwnMatch3
    namespaceName: 
    className: ObstacleSoundSystem
  - hash:
      serializedVersion: 2
      Hash: 36afd2b3383c61ddf671e7c29c69c388
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXInputMouseBinder
  - hash:
      serializedVersion: 2
      Hash: 39ce22f88c4c9c795740cdb54f992ad6
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXInputTouchBinder
  - hash:
      serializedVersion: 2
      Hash: b2d2a01a2a85d9ed12e80cb33a9a6044
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: AnimationTrack
  - hash:
      serializedVersion: 2
      Hash: 86dbb17f881954f07ae5bc215856a222
    assemblyName: UniTask
    namespaceName: Cysharp.Threading.Tasks.Triggers
    className: AsyncDestroyTrigger
  - hash:
      serializedVersion: 2
      Hash: be6a7898a1957a26cc16f1bfdd69c21e
    assemblyName: UniTask
    namespaceName: Cysharp.Threading.Tasks.Triggers
    className: AsyncStartTrigger
  - hash:
      serializedVersion: 2
      Hash: a5ca268a2c3e050356659335cd758dc2
    assemblyName: OwnMatch3
    namespaceName: 
    className: ObjectPoolerSettings
  - hash:
      serializedVersion: 2
      Hash: cf62aad97f6c2dd5d482e2509c6c9528
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TextMeshProUGUI
  - hash:
      serializedVersion: 2
      Hash: 793d75065c72cf2e9eba282c968ae7d4
    assemblyName: OwnMatch3
    namespaceName: DevsDaddy.GameShield.Demo.UI
    className: InGameView
  - hash:
      serializedVersion: 2
      Hash: 026a577b8baa11e6202524d5c176d148
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX
    className: VisualEffectControlClip
  - hash:
      serializedVersion: 2
      Hash: 615871896b3fb34cb9a4538477644819
    assemblyName: Assembly-CSharp
    namespaceName: Match3
    className: Gem
  - hash:
      serializedVersion: 2
      Hash: e20a6de5f1fd1dfae6f1186e8f77c3d9
    assemblyName: Unity.2D.Animation.Runtime
    namespaceName: UnityEngine.U2D.Animation
    className: SpriteResolver
  - hash:
      serializedVersion: 2
      Hash: 2c583ad0ac85d03e8700dd05fdcb46cb
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: Variables
  - hash:
      serializedVersion: 2
      Hash: 61319095e40c0cbfeeb89f99a1cdd4fb
    assemblyName: Unity.RenderPipelines.Universal.2D.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: CinemachineUniversalPixelPerfect
  - hash:
      serializedVersion: 2
      Hash: 29362692ab8b7c46bfb7c8cb42bfe5b4
    assemblyName: Unity.2D.Animation.Runtime
    namespaceName: UnityEngine.U2D.Animation
    className: SpriteLibrarySourceAsset
  - hash:
      serializedVersion: 2
      Hash: ed8e62f07ec878233f5493a335003493
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI
    className: VirtualMouseInput
  - hash:
      serializedVersion: 2
      Hash: b72d9c057c0f9460abf04218f4cc4f0e
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerMessageBox
  - hash:
      serializedVersion: 2
      Hash: ec59bc56f69d3d13d1fc9d3e087fb56d
    assemblyName: Unity.2D.Animation.Runtime
    namespaceName: UnityEngine.U2D.Animation
    className: SkeletonAsset
  - hash:
      serializedVersion: 2
      Hash: 0e130eac441e416c113b9b716f880f34
    assemblyName: OwnMatch3
    namespaceName: OwnMatch3.Monetization
    className: AdsSetupExample
  - hash:
      serializedVersion: 2
      Hash: 3c9ec9ef101f2636bd494d7da8a6dd9e
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputDebuggerWindow
  - hash:
      serializedVersion: 2
      Hash: 9ebc0a3016506a0cbb7e0306dfcb9497
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: DictionaryAsset
  - hash:
      serializedVersion: 2
      Hash: e7e95903ca66d58e1a7cbd71f824e16d
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SubMeshUI
  - hash:
      serializedVersion: 2
      Hash: 55cb64ec214a9b45331ea80a5fd58d34
    assemblyName: OwnMatch3
    namespaceName: OwnMatch3.Shop
    className: ShopManager
  - hash:
      serializedVersion: 2
      Hash: 0370b9f95798139b666659c7e1be6147
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI
    className: BaseInputOverride
  - hash:
      serializedVersion: 2
      Hash: c27554f9feec0ec62923b4c200a63b9e
    assemblyName: Unity.2D.IK.Runtime
    namespaceName: UnityEngine.U2D.IK
    className: CCDSolver2D
  - hash:
      serializedVersion: 2
      Hash: 0c0b64d103fe555d830e8ca206abd12a
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 0a8453a9d6cce42552fd51eea56653a1
    assemblyName: KinoBloom.Runtime
    namespaceName: Kino
    className: Bloom
  - hash:
      serializedVersion: 2
      Hash: 5853689a3479946eee2b8547c1f13d3c
    assemblyName: Unity.2D.Animation.Runtime
    namespaceName: UnityEngine.U2D.Animation
    className: DeformationManagerUpdater
  - hash:
      serializedVersion: 2
      Hash: febcfbf0a0d85e37808a3740e3c2c6fa
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: MarkerTrack
  - hash:
      serializedVersion: 2
      Hash: a119221a1c3d590645a52df2cbebb41c
    assemblyName: Unity.2D.Tilemap.Extras
    namespaceName: UnityEngine
    className: HexagonalRuleTile
  - hash:
      serializedVersion: 2
      Hash: 4392bfff79efe0630144f16ea4d656ab
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerObjectList
  - hash:
      serializedVersion: 2
      Hash: daeac1347f8039f61cd4017ff33bd3c6
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI
    className: InputSystemUIInputModule
  - hash:
      serializedVersion: 2
      Hash: e7787bf3b090a10110614be1e33b64a5
    assemblyName: OwnMatch3
    namespaceName: 
    className: SaveSystemDebugger
  - hash:
      serializedVersion: 2
      Hash: 8b15e35eb98fc258ec2e839df7c3c9b0
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnSliderValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: f99ce8526ec148f29051c0a9dced1a03
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: UniversalRenderPipelineGlobalSettings
  - hash:
      serializedVersion: 2
      Hash: 709e7a14fd69c48de1a0f460ef868042
    assemblyName: Unity.2D.Tilemap.Extras
    namespaceName: UnityEngine.Tilemaps
    className: AdvancedRuleOverrideTile
  - hash:
      serializedVersion: 2
      Hash: 5d95407a9adb4e92bf0b413c3d793233
    assemblyName: Unity.2D.Tilemap.Extras
    namespaceName: UnityEngine.Tilemaps
    className: AutoTile
  - hash:
      serializedVersion: 2
      Hash: 682dfc653b22feea53b38adb3bc66414
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: SignalReceiver
  - hash:
      serializedVersion: 2
      Hash: bfa9d6dfbc3c74a843a3b197bdcc6639
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_Settings
  - hash:
      serializedVersion: 2
      Hash: db9b308769d19e1f9e161df0392c23ca
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTransformChildrenChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 9dd54ce33440072d6692a67e0c384ad1
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: GroupTrack
  - hash:
      serializedVersion: 2
      Hash: f669be52680c88846b2093fd41d1e06e
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: 20a120e55ad3ab65556328d5fd8309dd
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Outline
  - hash:
      serializedVersion: 2
      Hash: 2deddcdd5f664164bb803747210cbba2
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerRow
  - hash:
      serializedVersion: 2
      Hash: c30f7d370472eaf3694b8fec6c66f25e
    assemblyName: OwnMatch3
    namespaceName: DevsDaddy.GameShield.Core.Editor
    className: AssembliesWhitelist
  - hash:
      serializedVersion: 2
      Hash: 2bbd05175d4cdd21e24f9716cdd24f83
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.XR
    className: TrackedPoseDriver
  - hash:
      serializedVersion: 2
      Hash: 1362033cd491d66ad3cc206af29cc0de
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: Volume
  - hash:
      serializedVersion: 2
      Hash: ec6b71b64c59731ca23f39758a6d32f6
    assemblyName: OwnMatch3
    namespaceName: 
    className: CurrencyTestController
  - hash:
      serializedVersion: 2
      Hash: da281dc879c7932e0c9669da88ae40c0
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerGroup
  - hash:
      serializedVersion: 2
      Hash: 84056876ce8b14176cdd7f82aaefd21a
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXVisibilityEventBinder
  - hash:
      serializedVersion: 2
      Hash: 6e5cab4b26d75c4a519f9c1929b96258
    assemblyName: Assembly-CSharp
    namespaceName: Match3
    className: ShopItemLive
  - hash:
      serializedVersion: 2
      Hash: 9507f221948bbd40853cc0ec75374d07
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: LensFlareComponentSRP
  - hash:
      serializedVersion: 2
      Hash: ed35b55622dd568f125b89356f0ebcee
    assemblyName: OwnMatch3
    namespaceName: OwnMatch3.Utils
    className: DebugSettings
  - hash:
      serializedVersion: 2
      Hash: 4ee1e5f285c826248e282a4df1635b19
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ChromaticAberration
  - hash:
      serializedVersion: 2
      Hash: 05afd034bf7a074278a40ad9e97d8db4
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: Vignette
  - hash:
      serializedVersion: 2
      Hash: f294bb42d237ff3f282e8652c9f7b475
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX
    className: SetSpawnTime
  - hash:
      serializedVersion: 2
      Hash: bfc8f8afc40cc7bc8a51bfd0a28d90c6
    assemblyName: OwnMatch3
    namespaceName: 
    className: AdaptiveGenerationSystem
  - hash:
      serializedVersion: 2
      Hash: 0bd2044f31f948e8e457585f08846860
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXTerrainBinder
  - hash:
      serializedVersion: 2
      Hash: e6ee7dccba41ae87d10e3d922c6cb71c
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_InputField
  - hash:
      serializedVersion: 2
      Hash: 522f6865b4ec12522e602206785309f8
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: GlobalMessageListener
  - hash:
      serializedVersion: 2
      Hash: 53e329840e6d03aebec76623c3b054db
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Slider
  - hash:
      serializedVersion: 2
      Hash: 29457b11bca46721896820509f1ed265
    assemblyName: OwnMatch3
    namespaceName: DevsDaddy.GameShield.Demo.UI
    className: DetectionView
  - hash:
      serializedVersion: 2
      Hash: 1d284ca8d84491e661a797b7976ac5ed
    assemblyName: OwnMatch3
    namespaceName: OwnMatch3.Monetization
    className: AdsGameIntegration
  - hash:
      serializedVersion: 2
      Hash: ef19274111491b6b8bcc6fd227f656f5
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerIndirectFloatField
  - hash:
      serializedVersion: 2
      Hash: ee34255e3e86289bde9bad5e07b84cbc
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnScrollRectValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 47d50f529890b9237021f88334dc1cf6
    assemblyName: Unity.2D.Animation.Runtime
    namespaceName: UnityEngine.U2D.Animation
    className: SpriteLibraryAsset
  - hash:
      serializedVersion: 2
      Hash: dd9dda1dfd39e605c49e72003342ef5d
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Image
  - hash:
      serializedVersion: 2
      Hash: fbf240d9f47cae80fb0950a655d80264
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.HID.Editor
    className: HIDDescriptorWindow
  - hash:
      serializedVersion: 2
      Hash: dffbd3da665050713c4b504a586a3ff5
    assemblyName: CFXRDemo
    namespaceName: CartoonFX
    className: CFXR_Demo_Translate
  - hash:
      serializedVersion: 2
      Hash: 66496b917404bcb20f6bf50861c1adc6
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: ProbeVolumeBakingSet
  - hash:
      serializedVersion: 2
      Hash: dbe4f05d59170430398f6453d7253fe5
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerWidget
  - hash:
      serializedVersion: 2
      Hash: dcf4e71c30705b910ef1e6c818fa0de0
    assemblyName: OwnMatch3
    namespaceName: OwnMatch3.UI
    className: DailyRewardsManager
  - hash:
      serializedVersion: 2
      Hash: 6d971045f8e67def7430d0b6f3cb0700
    assemblyName: Unity.RenderPipelines.GPUDriven.Runtime
    namespaceName: UnityEngine.Rendering
    className: DisallowSmallMeshCulling
  - hash:
      serializedVersion: 2
      Hash: 7bdff9e91edfd9600f92a185def7e34a
    assemblyName: Unity.VisualScripting.Flow
    namespaceName: Unity.VisualScripting
    className: ScriptMachine
  - hash:
      serializedVersion: 2
      Hash: 0a3f328f3ea906bbbcf794f4c27ee59d
    assemblyName: Unity.VisualScripting.Flow
    namespaceName: Unity.VisualScripting
    className: ScriptGraphAsset
  - hash:
      serializedVersion: 2
      Hash: 73707dd24af053d88322ad9a735a180a
    assemblyName: Unity.2D.Tilemap.Extras
    namespaceName: UnityEngine.Tilemaps
    className: GridInformation
  - hash:
      serializedVersion: 2
      Hash: 33f160a5c07e580c29aba2bd1e4db811
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: HorizontalLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: d9ff3bc93c7b71a675262f6f33f2031e
    assemblyName: Assembly-CSharp
    namespaceName: Match3
    className: BonusGem
  - hash:
      serializedVersion: 2
      Hash: de8e34fffe62b77ca47bae273d0414ba
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: ObsoleteProbeVolumeAsset
  - hash:
      serializedVersion: 2
      Hash: 02b5dba2a4c48b541064d2ece9a421b6
    assemblyName: OwnMatch3
    namespaceName: OwnMatch3
    className: AudioManager
  - hash:
      serializedVersion: 2
      Hash: b87547877f7bea0dd59c10bfb798b858
    assemblyName: Unity.2D.Tilemap.Extras
    namespaceName: UnityEngine
    className: IsometricRuleTile
  - hash:
      serializedVersion: 2
      Hash: 09ef1dd3fb40f5765e537fbc4f02f6ec
    assemblyName: Assembly-CSharp
    namespaceName: Match3
    className: MainMenu
  - hash:
      serializedVersion: 2
      Hash: 08ca933b8d87e9f3e26a80d40cb7ff57
    assemblyName: OwnMatch3
    namespaceName: OwnMatch3.UI.Particles
    className: ParticleElementPositionTest
  - hash:
      serializedVersion: 2
      Hash: 40b296372c7058ed362a1c6f831ab617
    assemblyName: OwnMatch3
    namespaceName: 
    className: BonusGemSystemExample
  - hash:
      serializedVersion: 2
      Hash: ccdadd00115b30810b51153e005995c8
    assemblyName: Assembly-CSharp
    namespaceName: Match3
    className: TieBlocker
  - hash:
      serializedVersion: 2
      Hash: c4cdb902228df56d5b2b639d6a6bbd3c
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: PlayerInputManager
  - hash:
      serializedVersion: 2
      Hash: 5ee5963bc12be6b3130350bfffadf064
    assemblyName: OwnMatch3
    namespaceName: OwnMatch3.UI.Particles
    className: ParticleElementPropertyTester
  - hash:
      serializedVersion: 2
      Hash: 1bc6ff8d02d2d68019aa6a028f8d409d
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerUpMessageListener
  - hash:
      serializedVersion: 2
      Hash: e987953190eb4c12cf19df790566dc2c
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: EventSystem
  - hash:
      serializedVersion: 2
      Hash: f6d2ec73741a17fb6035f3bfaa050bd0
    assemblyName: OwnMatch3
    namespaceName: DevsDaddy.GameShield.Core
    className: GameShieldConfig
  - hash:
      serializedVersion: 2
      Hash: 50b528aea450b1516d9f0d18cc2f296c
    assemblyName: Unity.LevelPlay
    namespaceName: Unity.Services.LevelPlay
    className: BannerPrefab
  - hash:
      serializedVersion: 2
      Hash: 3f7fd8381bb04913fb787b19a7ae65ad
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX
    className: IncrementStripIndexOnStart
  - hash:
      serializedVersion: 2
      Hash: 0bc7ef1b7516f21320ec49bfd31eef2e
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI
    className: TrackedDeviceRaycaster
  - hash:
      serializedVersion: 2
      Hash: aef0d0885a03f8f85e8bff9026f542df
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXTransformBinder
  - hash:
      serializedVersion: 2
      Hash: 4bc231fa82f186ecdde50f74e3b56a2c
    assemblyName: OwnMatch3
    namespaceName: DevsDaddy.GameShield.Core.Editor.Tools
    className: SecuredTypePatcher
  - hash:
      serializedVersion: 2
      Hash: 33656f83bb0079184b2206a69690ec46
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: AspectRatioFitter
  - hash:
      serializedVersion: 2
      Hash: da5a949a0bbd2ff91bc51c5805c2c41f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseUpAsButtonMessageListener
  - hash:
      serializedVersion: 2
      Hash: 9104447b683bf70406fa01f12ecb564a
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: StandaloneInputModule
  - hash:
      serializedVersion: 2
      Hash: ea5e0cb2f62c5bafc3dfb7bcdc9595e1
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: DebugUpdater
  - hash:
      serializedVersion: 2
      Hash: a92561298b80715aa69e8fa770123cb5
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI.Editor
    className: InputSystemUIInputModuleEditor
  - hash:
      serializedVersion: 2
      Hash: 9b30f6a419c7dd57a95d40dd7ce1c6b2
    assemblyName: Assembly-CSharp
    namespaceName: Match3
    className: LightManager
  - hash:
      serializedVersion: 2
      Hash: b14e186823458f549289789cccbb77ce
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerDownMessageListener
  - hash:
      serializedVersion: 2
      Hash: 67272c9c30c50b68ef1ce72ac0191c57
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMoveMessageListener
  - hash:
      serializedVersion: 2
      Hash: 095fb569bb43a1505544eebd33a90567
    assemblyName: Unity.Services.Core.Components
    namespaceName: Unity.Services.Core.Components
    className: ServicesInitialization
  - hash:
      serializedVersion: 2
      Hash: 6b2b20d5352b9f1294595326f54bd3c2
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerObjectPopupField
  - hash:
      serializedVersion: 2
      Hash: 191c3402cd43ee08f0a96795440c294e
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: AdvancedDropdownWindow
  - hash:
      serializedVersion: 2
      Hash: fce99de7ab700e0c4c466ebd583aa306
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ColorLookup
  - hash:
      serializedVersion: 2
      Hash: 915204dac8acad99f076d6e72ada0a0f
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Button
  - hash:
      serializedVersion: 2
      Hash: c28962ef689b0c7d226acbfb0df8c443
    assemblyName: OwnMatch3
    namespaceName: DevsDaddy.GameShield.Core.Editor.Tools
    className: SecuredTypeConverterDocumentation
  - hash:
      serializedVersion: 2
      Hash: 95426d463074947dcbffc797c44e779c
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI.Editor
    className: StandaloneInputModuleModuleEditor
  - hash:
      serializedVersion: 2
      Hash: 9f27b94c3c17b0da0d39632b61a4b711
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: Bloom
  - hash:
      serializedVersion: 2
      Hash: c0a2b4d13675f08bfb211e044c334e68
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerIntField
  - hash:
      serializedVersion: 2
      Hash: 090b4014813624206b1713d7decd7b0d
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXInputKeyBinder
  - hash:
      serializedVersion: 2
      Hash: e63fe08659e7e4647a51098c666f8845
    assemblyName: Unity.InputSystem
    namespaceName: 
    className: DownloadableSample
  - hash:
      serializedVersion: 2
      Hash: afb7b65d28d5a7423e69a1b076be0064
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: PositionAsUV1
  - hash:
      serializedVersion: 2
      Hash: 4728e3d21cc2a12e391e71ec9b7eed0b
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: SignalTrack
  - hash:
      serializedVersion: 2
      Hash: 422ac9cf78bc74877e349bb9fb4f006b
    assemblyName: Unity.InferenceEngine
    namespaceName: Unity.InferenceEngine
    className: ModelAsset
  - hash:
      serializedVersion: 2
      Hash: b967c7dd04279a1f14351af82cb5efdb
    assemblyName: OwnMatch3
    namespaceName: 
    className: StarAnimationTester
  - hash:
      serializedVersion: 2
      Hash: b30c8635462e66ac20f4241106119f77
    assemblyName: Unity.VisualScripting.State
    namespaceName: Unity.VisualScripting
    className: StateMachine
  - hash:
      serializedVersion: 2
      Hash: 8a3bbd2b89ca9a0a0af572108b609a25
    assemblyName: OwnMatch3
    namespaceName: OwnMatch3.UI
    className: MissionsManager
  - hash:
      serializedVersion: 2
      Hash: 301b73e224df551821543e7fc3ef66a6
    assemblyName: OwnMatch3
    namespaceName: 
    className: StarCalculationTest
  - hash:
      serializedVersion: 2
      Hash: 3b5263a98bcc71d9a9d1d78a31b34ced
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SelectionCaret
  - hash:
      serializedVersion: 2
      Hash: 8e390afb43ed5d52e0f1c1e0d7be0657
    assemblyName: OwnMatch3
    namespaceName: DevsDaddy.GameShield.Core.Components
    className: SecuredTypeAutoFix
  - hash:
      serializedVersion: 2
      Hash: d41ab9b5ea92474f9c0a6937a0607ce1
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: ContentSizeFitter
  - hash:
      serializedVersion: 2
      Hash: 0f6b6107168e347e854c0107aa8cb9fb
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerEnumHistory
  - hash:
      serializedVersion: 2
      Hash: 51b80d3734ea86b38e3101db98029e15
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: AnimationPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: 7d026ae582dc02c5edc3fc23b8c03ff7
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerVector3
  - hash:
      serializedVersion: 2
      Hash: cecd6bdbd2a2de437609ccfa348e6f80
    assemblyName: Unity.LevelPlay
    namespaceName: 
    className: IronSourceInterstitialEvents
  - hash:
      serializedVersion: 2
      Hash: b72839eb6e4350778dc879d98832aa2b
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerHBox
  - hash:
      serializedVersion: 2
      Hash: 423a204f105739da55558e8e0d136a67
    assemblyName: Unity.AI.Navigation
    namespaceName: Unity.AI.Navigation
    className: NavMeshModifier
  - hash:
      serializedVersion: 2
      Hash: 99df57f0e99b6c37d6787662077398cb
    assemblyName: EventFramework
    namespaceName: DevsDaddy.Shared.EventFramework.Core.Threading
    className: MainThreadDispatcher
  - hash:
      serializedVersion: 2
      Hash: 67ba98f8a8977f3e60e5f0e049923033
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerColor
  - hash:
      serializedVersion: 2
      Hash: 93210aa0c6af494fcde7d151440ae219
    assemblyName: Assembly-CSharp
    namespaceName: Match3
    className: LevelData
  - hash:
      serializedVersion: 2
      Hash: bc8c525e39b6fddab1a15c5b47455669
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ScreenSpaceLensFlare
  - hash:
      serializedVersion: 2
      Hash: 7bb32d6e4c7614a97d9723ce7e26588c
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: BasicPlayableBehaviour
  - hash:
      serializedVersion: 2
      Hash: d0cdd713f9bf4e6a625d9e7804deca42
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: EventTrigger
  - hash:
      serializedVersion: 2
      Hash: 3fd3a74e10cdfd45658891dfffc27a90
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: PostProcessData
  - hash:
      serializedVersion: 2
      Hash: 01727a040aa965d7fae41c3c1e1d9ee5
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: PaniniProjection
  - hash:
      serializedVersion: 2
      Hash: a9a9985bc600ab81586355efc506ee17
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: WhiteBalance
  - hash:
      serializedVersion: 2
      Hash: 21d396433bc155436b5004301182fbfa
    assemblyName: Unity.2D.PixelPerfect
    namespaceName: UnityEngine.U2D
    className: CinemachinePixelPerfect
  - hash:
      serializedVersion: 2
      Hash: c89560eb9ccbe5ee3b097c599f56bf4a
    assemblyName: OwnMatch3
    namespaceName: 
    className: GemSoundSystem
  - hash:
      serializedVersion: 2
      Hash: e88d71ef93aba4e7faf243430f6a10ce
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: MacroScriptableObject
  - hash:
      serializedVersion: 2
      Hash: faccd05cbe8b025f92660e27a727fe62
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXPositionBinder
  - hash:
      serializedVersion: 2
      Hash: 651f5613cff97a89b190b4cf102c278b
    assemblyName: Assembly-CSharp
    namespaceName: Match3
    className: InitLoader
  - hash:
      serializedVersion: 2
      Hash: 7c9f0c01c9392fbb8328774804daaf58
    assemblyName: OwnMatch3
    namespaceName: 
    className: GetTimeNetInitializer
  - hash:
      serializedVersion: 2
      Hash: 991def3bb38fe468342c445b01daabcf
    assemblyName: Unity.2D.Animation.Runtime
    namespaceName: UnityEngine.U2D.Animation
    className: DeformationManager
  - hash:
      serializedVersion: 2
      Hash: 4f353524ce6564e40764f1ea080b2d85
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerStay2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 99a378b25d7f7f0ee01e6a4feddd9667
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: DecalProjector
  - hash:
      serializedVersion: 2
      Hash: 02a47340661d2ea7c6d30e292cfef403
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnToggleValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: a3901985eb076c1c9b7562fca82ca3e0
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: SignalAsset
  - hash:
      serializedVersion: 2
      Hash: 5bc59a9ddf7c5507bf172212e9aa32bc
    assemblyName: OwnMatch3
    namespaceName: 
    className: LogToFile
  - hash:
      serializedVersion: 2
      Hash: f295412bc00b58b9095a71e9764cc886
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: AudioTrack
  - hash:
      serializedVersion: 2
      Hash: 6b340130e39eaf5ad803bafb9d8a511f
    assemblyName: OwnMatch3
    namespaceName: 
    className: ScoreProgressBarController
  - hash:
      serializedVersion: 2
      Hash: 8e6dc3d79712ca4934f6d50c80f010b1
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SpriteAnimator
  - hash:
      serializedVersion: 2
      Hash: 84e614ebb30d49cd0e8d226dea61002f
    assemblyName: OwnMatch3
    namespaceName: Match3
    className: PreGamePopupController
  - hash:
      serializedVersion: 2
      Hash: c6369b4cc3be620d6c9300e920bc3d52
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: VariablesAsset
  - hash:
      serializedVersion: 2
      Hash: cf32d98954e63b8e30e9334b152e9b8e
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: BaseInput
  - hash:
      serializedVersion: 2
      Hash: aa8a6eda5034396ddb8f5e28fc18ecf6
    assemblyName: Unity.LevelPlay
    namespaceName: 
    className: IronSourceRewardedVideoEvents
  - hash:
      serializedVersion: 2
      Hash: 08e2446b65d2da7600c6bbdd36de0e56
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: InputSystemObject
  - hash:
      serializedVersion: 2
      Hash: 3e21c4fc1790539a1a5e53c6dae8099a
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ChannelMixer
  - hash:
      serializedVersion: 2
      Hash: 97b6bd8e72ee2f269225538187a059d3
    assemblyName: Unity.AppUI
    namespaceName: Unity.AppUI.Core
    className: AppUIManagerBehaviour
  - hash:
      serializedVersion: 2
      Hash: b24bae4511d404331640e62f8b8c2ed0
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: ProbeVolumesOptions
  - hash:
      serializedVersion: 2
      Hash: 24798e7e017ce8880357f0e6a77faa5c
    assemblyName: OwnMatch3
    namespaceName: 
    className: AdaptiveConfig
  - hash:
      serializedVersion: 2
      Hash: 2a2eef56932c95206cbf2fb724fb40af
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXMultiplePositionBinder
  - hash:
      serializedVersion: 2
      Hash: 8dff8e9853297d341afa0c11f9091b59
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: LayoutElement
  - hash:
      serializedVersion: 2
      Hash: bae53e56d25743f35f28d9e1407add4f
    assemblyName: OwnMatch3
    namespaceName: 
    className: ScoreProgressBarExample
  - hash:
      serializedVersion: 2
      Hash: 00bcfff150a932126a4ea840d0861a6b
    assemblyName: Unity.2D.SpriteShape.Runtime
    namespaceName: UnityEngine.U2D
    className: SpriteShapeDefaultCreator
  - hash:
      serializedVersion: 2
      Hash: f3cba38773b3d809de81d0b2699a038c
    assemblyName: Unity.LevelPlay
    namespaceName: Unity.Services.LevelPlay
    className: InterstitialPrefab
  - hash:
      serializedVersion: 2
      Hash: 1321993db8249e1e05e1e0492b2bbdc3
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnBecameVisibleMessageListener
  - hash:
      serializedVersion: 2
      Hash: d90629714fa6b0145fcaffc87f5c6559
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnInputFieldValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: b6307dfc54cca9ba44f47185c578b0ce
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: 2ba5503def171efd088377e85d5a988e
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: URPCameraBinder
  - hash:
      serializedVersion: 2
      Hash: fe0a072dbdcbca34d5003c5299de3aaf
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: SceneRenderPipeline
  - hash:
      serializedVersion: 2
      Hash: 8dc40a01667ca02b3354a4e7a61be082
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: GridLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: 9ed84ccc7f822799f8d08159e2a3e77d
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXMouseEventBinder
  - hash:
      serializedVersion: 2
      Hash: a1be25ac98e4fc944484221fbb53e91a
    assemblyName: OwnMatch3
    namespaceName: 
    className: BonusGemSystemSetup
  - hash:
      serializedVersion: 2
      Hash: 26e0c18b1b2bfc8e5521a0263770be28
    assemblyName: OwnMatch3
    namespaceName: DevsDaddy.GameShield.Demo.UI
    className: DialogView
  - hash:
      serializedVersion: 2
      Hash: bf0842d88681628b29beb00b5e2ab785
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnDeselectMessageListener
  - hash:
      serializedVersion: 2
      Hash: 339fb8a0a43f5c808447ae6d54e7f176
    assemblyName: Unity.AppUI.Navigation
    namespaceName: Unity.AppUI.Navigation
    className: NavGraphViewNode
  - hash:
      serializedVersion: 2
      Hash: bc75926bfd3609757f7bf33ff766f026
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.EnhancedTouch
    className: TouchSimulation
  - hash:
      serializedVersion: 2
      Hash: 33eb495d99aa9bea4dbe8b4c4e02c7bb
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: InputActionReference
  - hash:
      serializedVersion: 2
      Hash: 4878e735441f4041539853759559c86f
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: VerticalLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: e0cf060d3a0d1afae0c661ac42606ab8
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: ProbeVolume
  - hash:
      serializedVersion: 2
      Hash: f960c923386736f8c2005b9765968c31
    assemblyName: Assembly-CSharp
    namespaceName: Match3
    className: GameManager
  - hash:
      serializedVersion: 2
      Hash: 40be2ad159aa1a2b72ecc74cb38c7824
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI
    className: MultiplayerEventSystem
  - hash:
      serializedVersion: 2
      Hash: 22130d378b859a59f2cb973162c02f89
    assemblyName: Unity.2D.PixelPerfect
    namespaceName: UnityEngine.U2D
    className: PixelPerfectCamera
  - hash:
      serializedVersion: 2
      Hash: 715f7928b39881049ce8c46350cb8681
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTransformParentChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 13ef8aa3a81c847a48be0718b121cef4
    assemblyName: Unity.AppUI.Navigation
    namespaceName: Unity.AppUI.Navigation
    className: NavGraph
  - hash:
      serializedVersion: 2
      Hash: 6cf63e9dc888f92a3672d2e4db631c8e
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: PlayerInputManagerEditor
  - hash:
      serializedVersion: 2
      Hash: 3074afe1b03a0fb081e176a4ef1b9d09
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionAssetEditor
  - hash:
      serializedVersion: 2
      Hash: 891e8502b8fbce4228ea9f0b9ed2c98a
    assemblyName: Unity.AppUI
    namespaceName: Unity.AppUI.Core
    className: WorldSpaceUIDocument
  - hash:
      serializedVersion: 2
      Hash: 113e54f67456d0e7754c0598ee2a6dd4
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: ProbeVolumePerSceneData
  - hash:
      serializedVersion: 2
      Hash: 28fd3d045acc780719a17f02073eb448
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: InputField
  - hash:
      serializedVersion: 2
      Hash: 4001f3727b45ffa51ecc45ba969b4108
    assemblyName: Unity.Recorder
    namespaceName: UnityEngine.Recorder
    className: RecorderBindings
  - hash:
      serializedVersion: 2
      Hash: 831a07ec3647706fc718cb04ae39f2c9
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXPropertyBinder
  - hash:
      serializedVersion: 2
      Hash: e49f64f6c7006f2a8a0e7674b5de1fae
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionDebuggerWindow
  - hash:
      serializedVersion: 2
      Hash: 919dc7ec08f773fcae2d21a1e018bd99
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: MotionBlur
  - hash:
      serializedVersion: 2
      Hash: 14fb963022965ffa004cc4ec810b9fdd
    assemblyName: Unity.2D.Animation.Runtime
    namespaceName: UnityEngine.U2D.Animation
    className: BufferManager
  - hash:
      serializedVersion: 2
      Hash: 90cf308600fb4aeb677786843453ec55
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: RemoteInputPlayerConnection
  - hash:
      serializedVersion: 2
      Hash: c833d8ddc320fe5153f6a88cef23c858
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerObject
  - hash:
      serializedVersion: 2
      Hash: 49e5c84a0e3425cf79c773c13e262436
    assemblyName: OwnMatch3
    namespaceName: 
    className: LevelSelectionUI
  - hash:
      serializedVersion: 2
      Hash: 5c2a5af4658ede896d072a8a641d706e
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ColorCurves
  - hash:
      serializedVersion: 2
      Hash: 47f78e94e3df8d612cefa1307a19e2ce
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerValue
  - hash:
      serializedVersion: 2
      Hash: b091df1d5a72650c0ea012ab3040b06b
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerRenderingLayerField
  - hash:
      serializedVersion: 2
      Hash: 81abcac4f15fd2dfc76ce16711dd5df5
    assemblyName: CFXRRuntime
    namespaceName: CartoonFX
    className: CFXR_ParticleTextFontAsset
  - hash:
      serializedVersion: 2
      Hash: 156e723c16557777eb3b9a7ed1f28052
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: UniversalRendererData
  - hash:
      serializedVersion: 2
      Hash: 4fcf0bb4aff1d14a1dfcdedc4b07bd05
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: TouchInputModule
  - hash:
      serializedVersion: 2
      Hash: 8d7ab1303b1c14da5fdd62bf96ca6e89
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: FreeCamera
  - hash:
      serializedVersion: 2
      Hash: 34bcbd4b9a8d1ee94b65669777a5e8b4
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXInputButtonBinder
  - hash:
      serializedVersion: 2
      Hash: 8703b6ae2b0eefd9dafbc44d8d333fb1
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Shadow
  - hash:
      serializedVersion: 2
      Hash: 2be3311c714ed7d9f0acfbd89c026485
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXVelocityBinder
  - hash:
      serializedVersion: 2
      Hash: 93e915dd395abf7a96e694ce213db8cd
    assemblyName: OwnMatch3
    namespaceName: 
    className: ScoreManager
  - hash:
      serializedVersion: 2
      Hash: 1ab136900c0edaeeb49424c852595030
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: RawImage
  - hash:
      serializedVersion: 2
      Hash: 3889300aec5dd5929f32ac3dcec2af62
    assemblyName: Assembly-CSharp
    namespaceName: Match3
    className: GemPlacerTile
  - hash:
      serializedVersion: 2
      Hash: 1b32c5551298e17b2b3237d120b9537b
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: UniversalAdditionalCameraData
  - hash:
      serializedVersion: 2
      Hash: 167a2524547631ea2d29a584165d2e16
    assemblyName: OwnMatch3
    namespaceName: DevsDaddy.GameShield.Core.Editor.Tools
    className: SafeSecuredTypeConverter
  - hash:
      serializedVersion: 2
      Hash: 96e404889ca7898558202dce733f7f0b
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputStateWindow
  - hash:
      serializedVersion: 2
      Hash: 7053e305cdc1eb560f4c3be49d3b5136
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: PhysicsRaycaster
  - hash:
      serializedVersion: 2
      Hash: e5f9868e485ac5248f96a3be584a5e4a
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: ControlPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: 810e3c228b295cc60c4de2cdd94b18cb
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SpriteAsset
  - hash:
      serializedVersion: 2
      Hash: f2098fcd8ee983a05ec192f63904298f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnEndDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: c058ff8faaff3e0da63b5433ebfb40bc
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: Tonemapping
  - hash:
      serializedVersion: 2
      Hash: 953fad5c5ab1e7bb889420c434db93fe
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerFloatField
  - hash:
      serializedVersion: 2
      Hash: 27572486f9f5020d084af7c35340f14c
    assemblyName: OwnMatch3
    namespaceName: OwnMatch3.UI
    className: EmailManager
  - hash:
      serializedVersion: 2
      Hash: a908b098f2284663c0b9130e88844772
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerCanvas
  - hash:
      serializedVersion: 2
      Hash: 4604cc292099cd4847e99c9e6b54bf5e
    assemblyName: OwnMatch3
    namespaceName: 
    className: UIManager
  - hash:
      serializedVersion: 2
      Hash: c4b85a67513b4f0cf7b5fb2d5ded04b0
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: fd7983aadddd0b1b9f4ce82ed537bde5
    assemblyName: Unity.2D.SpriteShape.Runtime
    namespaceName: UnityEngine.U2D
    className: SpriteShape
  - hash:
      serializedVersion: 2
      Hash: 711ba963e2b245a25de71beeaeeeb658
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerFoldout
  - hash:
      serializedVersion: 2
      Hash: 82070af232e8767572315885c22ca7b7
    assemblyName: Assembly-CSharp
    namespaceName: Match3
    className: ObstaclePlacer
  - hash:
      serializedVersion: 2
      Hash: 1131d16e2402427640760042b5486b11
    assemblyName: Unity.2D.IK.Runtime
    namespaceName: UnityEngine.U2D.IK
    className: CullingManager
  - hash:
      serializedVersion: 2
      Hash: 9f355e210e7950e47ad1391c7681435b
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerEnumField
  - hash:
      serializedVersion: 2
      Hash: 43ebd945b04bced376c791411cf5a289
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnSelectMessageListener
  - hash:
      serializedVersion: 2
      Hash: 641db25e60d92b5bd7b82bd264214ee6
    assemblyName: OwnMatch3
    namespaceName: DevsDaddy.GameShield.Demo.UI.Elements
    className: CaptchaButton
  - hash:
      serializedVersion: 2
      Hash: a9f0153fc675e1f4ade818b83e4ff0c1
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: VolumeComponent
  - hash:
      serializedVersion: 2
      Hash: 209c1378f23c45375951c1715943159f
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXTriggerEventBinder
  - hash:
      serializedVersion: 2
      Hash: 13f262cc0741566a7ab72682984bc3e1
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SubMesh
  - hash:
      serializedVersion: 2
      Hash: b4678500d6f0b260b0a67a213bcaf739
    assemblyName: Google.Play.Common
    namespaceName: Google.Play.Common.LoadingScreen
    className: LoadingScreen
  - hash:
      serializedVersion: 2
      Hash: ea3d1607dbb3a2a766daffc34d07d1fe
    assemblyName: OwnMatch3
    namespaceName: 
    className: ObjectPoolerEditorWindow
  - hash:
      serializedVersion: 2
      Hash: 0f5a086d4d028363983ba6ca30a01397
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseOverMessageListener
  - hash:
      serializedVersion: 2
      Hash: a22de4fce6585f2a0cdcfc12c7323bee
    assemblyName: Unity.AppUI.Navigation
    namespaceName: Unity.AppUI.Navigation
    className: NavAction
  - hash:
      serializedVersion: 2
      Hash: 219eb03f3a7260c7b8f501c7f7a41c26
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ScreenSpaceAmbientOcclusion
  - hash:
      serializedVersion: 2
      Hash: e23d2210701e34e6850023cabe554c1f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnInputFieldEndEditMessageListener
  - hash:
      serializedVersion: 2
      Hash: 4ed922ab9c8d8110acb5e0e261afa93a
    assemblyName: Unity.InferenceEngine
    namespaceName: Unity.InferenceEngine
    className: ModelAssetWeightsData
  - hash:
      serializedVersion: 2
      Hash: 92bcd3e81a43d818299563966b30406c
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ForwardRendererData
  - hash:
      serializedVersion: 2
      Hash: a624e8926762e588c5e4bdc7ed77080f
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXHierarchyAttributeMapBinder
  - hash:
      serializedVersion: 2
      Hash: 32c374bcf9ddef3506f3a75bca7ea522
    assemblyName: OwnMatch3
    namespaceName: 
    className: GameSecurity
  - hash:
      serializedVersion: 2
      Hash: a90f6a00b23520788be7d89ee3b0773d
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Scrollbar
  - hash:
      serializedVersion: 2
      Hash: 5124a0cfb9e335814a7a8eb88ed00b85
    assemblyName: CFXRRuntime
    namespaceName: CartoonFX
    className: CFXR_Effect
  - hash:
      serializedVersion: 2
      Hash: e0489cb02bdeee1fc85b0d1a0dbcf838
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXEnabledBinder
  - hash:
      serializedVersion: 2
      Hash: 8ed1980cbef176651b026ecb679ad294
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXRigidBodyCollisionEventBinder
  - hash:
      serializedVersion: 2
      Hash: 4b2145e23c2e504506327fb00be2f02c
    assemblyName: Assembly-CSharp
    namespaceName: Match3
    className: ShopItemBonusItem
  - hash:
      serializedVersion: 2
      Hash: 80aeb876b0e95f6256bbfcbf33e26e84
    assemblyName: Unity.2D.IK.Runtime
    namespaceName: UnityEngine.U2D.IK
    className: LimbSolver2D
  - hash:
      serializedVersion: 2
      Hash: f5df081962c06a171f380eb236cd1f3c
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerVBox
  - hash:
      serializedVersion: 2
      Hash: 7eac76d211207b249baca0fa3a1049c0
    assemblyName: OwnMatch3
    namespaceName: OwnMatch3.UI.Particles
    className: SimpleParticleController
  - hash:
      serializedVersion: 2
      Hash: 215d2dc6ec6ea06728398ea39a103cb3
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.OnScreen
    className: OnScreenStick
  - hash:
      serializedVersion: 2
      Hash: 95abc51e6258cc2519640a1685a0cfac
    assemblyName: OwnMatch3
    namespaceName: DevsDaddy.GameShield.Core.Editor.Tools
    className: SecuredTypeBatchConverter
  - hash:
      serializedVersion: 2
      Hash: 3d26478faca6e0bb31b2897cb8da4060
    assemblyName: CFXRRuntime
    namespaceName: CartoonFX
    className: CFXR_ParticleText
  - hash:
      serializedVersion: 2
      Hash: e8fd74e57e72e8e4ef28792881adb864
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerUIntField
  - hash:
      serializedVersion: 2
      Hash: 58570e231c93c2f96918856759b81286
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerPanel
  - hash:
      serializedVersion: 2
      Hash: 35ae5f5ee8eec54a7d3f9f94261d9585
    assemblyName: OwnMatch3
    namespaceName: OwnMatch3.Monetization
    className: AdsIntegrationHelper
  - hash:
      serializedVersion: 2
      Hash: 13d4eef6fc0f2a3a8b62bda6dfef215e
    assemblyName: Assembly-CSharp
    namespaceName: Match3
    className: Crate
  - hash:
      serializedVersion: 2
      Hash: e8d390bd9ecacc453a7500b90d0c0292
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: Physics2DRaycaster
  - hash:
      serializedVersion: 2
      Hash: a89b0448af4e1cd103a77f9fec0a961f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnSubmitMessageListener
  - hash:
      serializedVersion: 2
      Hash: f2573bd1ab40ea8dbb27ba5bdbebb6b0
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXSphereBinder
  - hash:
      serializedVersion: 2
      Hash: 46474a07dc1f991d7b38a1ce10bfde02
    assemblyName: Google.Play.AppUpdate
    namespaceName: Google.Play.AppUpdate.Internal
    className: AppUpdateHandler
  - hash:
      serializedVersion: 2
      Hash: 5a4aa2850fe11587f47e34c270c7fd22
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: GraphicRaycaster
  - hash:
      serializedVersion: 2
      Hash: 86344100c716167fb86137c127ca9eee
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionImporterEditor
  - hash:
      serializedVersion: 2
      Hash: e6f8333cc8a800e2866870a5ef7efc35
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnControllerColliderHitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 64d58aae5d24531c22e8ae2d88523020
    assemblyName: OwnMatch3
    namespaceName: DevsDaddy.GameShield.Core.Editor.Tools
    className: SecuredTypeConverter
  - hash:
      serializedVersion: 2
      Hash: 1ce63d97d1ca25cba2d91d5f32fd5f79
    assemblyName: Unity.VisualScripting.State
    namespaceName: Unity.VisualScripting
    className: StateGraphAsset
  - hash:
      serializedVersion: 2
      Hash: 8ed5daadd237bb82e132790f75ffccaf
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: PlayerInputEditor
  - hash:
      serializedVersion: 2
      Hash: 568292fb7ea6c4f7a667e5ff76bc7e85
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseUpMessageListener
  - hash:
      serializedVersion: 2
      Hash: 504a9ea1f43c5247895178ca7dcfb821
    assemblyName: OwnMatch3
    namespaceName: 
    className: GameUIDocument
  - hash:
      serializedVersion: 2
      Hash: 55060a7bf226ac385ce19ebd450f199f
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UIElements
    className: PanelRaycaster
  - hash:
      serializedVersion: 2
      Hash: 8ca4b08ac83c98731c5b520ae08d0608
    assemblyName: Assembly-CSharp
    namespaceName: Match3
    className: LevelList
  - hash:
      serializedVersion: 2
      Hash: 7e12302f66e3c8ed5bcfea4ce65fccea
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityMessageListener
  - hash:
      serializedVersion: 2
      Hash: 6d94138b60a9299798dda96c1ee6725f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnBeginDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: 785d5756c0054e6e7f4603ab4e760d3f
    assemblyName: OwnMatch3
    namespaceName: 
    className: RocketTrailEffect
  - hash:
      serializedVersion: 2
      Hash: 51831895865c35dbf5b92151d1649810
    assemblyName: OwnMatch3
    namespaceName: 
    className: StageProgressBarExample
  - hash:
      serializedVersion: 2
      Hash: 6c3846f876af469779892bb62195da1a
    assemblyName: Unity.2D.SpriteShape.Runtime
    namespaceName: UnityEngine.U2D
    className: SpriteShapeController
  - hash:
      serializedVersion: 2
      Hash: b891829df48a906726622589c524dfa9
    assemblyName: OwnMatch3
    namespaceName: DevsDaddy.GameShield.Core.Editor.Tools
    className: QuickDataFix
  - hash:
      serializedVersion: 2
      Hash: ae0d197c8dfcf64de197f8adbff29da2
    assemblyName: Unity.LevelPlay
    namespaceName: 
    className: IronSourceMediationSettings
  - hash:
      serializedVersion: 2
      Hash: c71af0528f8f894422b91b128a0d275d
    assemblyName: OwnMatch3
    namespaceName: 
    className: ObjectPoolerGenericExamples
  - hash:
      serializedVersion: 2
      Hash: 628e11e2575086a49152a3c46b05153e
    assemblyName: Assembly-CSharp
    namespaceName: Match3
    className: LargeBomb
  - hash:
      serializedVersion: 2
      Hash: 1553f209ae0b2d5d3a35685fca55f9c3
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_ScrollbarEventHandler
  - hash:
      serializedVersion: 2
      Hash: 2850bfdae66f96afe19d1545c3866a6e
    assemblyName: OwnMatch3
    namespaceName: 
    className: ProgressDisplayUI
  - hash:
      serializedVersion: 2
      Hash: 8bf800a764665b085fe469e8aea7f167
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: 13cdd183600eb53a1e956a7536d106d7
    assemblyName: Unity.LevelPlay
    namespaceName: 
    className: IronSourceBannerEvents
  - hash:
      serializedVersion: 2
      Hash: f854af586011dd194574f89546642e29
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 1f83eef13a79f436d9b929359da21ab6
    assemblyName: Unity.RenderPipelines.Universal.2D.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ShadowCaster2D
  - hash:
      serializedVersion: 2
      Hash: d4c7cb9e46d192844a5b295d6a3f8dc5
    assemblyName: Unity.RenderPipelines.GPUDriven.Runtime
    namespaceName: UnityEngine.Rendering
    className: DisallowGPUDrivenRendering
  - hash:
      serializedVersion: 2
      Hash: 10ffd5579f941e0f4946756d18f1a9df
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerValueTuple
  - hash:
      serializedVersion: 2
      Hash: f34cc490b6857195b9cdb30787d4b96f
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UIElements
    className: WorldDocumentRaycaster
  - hash:
      serializedVersion: 2
      Hash: c78ed13f6b5a632d70d4f73f56ad81f2
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: VariablesSaver
  - hash:
      serializedVersion: 2
      Hash: 1a680d9a08de79b0d2c779e78c9843b4
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: RectMask2D
  - hash:
      serializedVersion: 2
      Hash: bc1ea34faea3941b47998ef2fc58d503
    assemblyName: OwnMatch3
    namespaceName: OwnMatch3.UI.Particles
    className: ParticleElementTester
  - hash:
      serializedVersion: 2
      Hash: ad4dc1dcebd873a43c9df353e385f0a5
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputDeviceDebuggerWindow
  - hash:
      serializedVersion: 2
      Hash: bf79486b4a418ef776af0ff654b40207
    assemblyName: Google.Play.Games
    namespaceName: GooglePlayGames.OurUtils
    className: NearbyHelperObject
  - hash:
      serializedVersion: 2
      Hash: f73e3a97805e1e671360c632c4565101
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: FilmGrain
  - hash:
      serializedVersion: 2
      Hash: d1e251339714fe85c544f2ebff0b9290
    assemblyName: UniTask
    namespaceName: Cysharp.Threading.Tasks.Triggers
    className: AsyncAwakeTrigger
  - hash:
      serializedVersion: 2
      Hash: 88d8a09a7044246cfa980ab477233baa
    assemblyName: OwnMatch3
    namespaceName: OwnMatch3.Boosters
    className: BoosterConfiguration
  - hash:
      serializedVersion: 2
      Hash: aff377e2d039c386943a66579ceb7b6b
    assemblyName: OwnMatch3
    namespaceName: 
    className: FrameBuilder
  - hash:
      serializedVersion: 2
      Hash: 703ab971c7156a6eab5c372829d0e751
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: 
    className: FullScreenPassRendererFeature_OldGUID
  - hash:
      serializedVersion: 2
      Hash: e252e2522be251918afa38a5cfb9cdf6
    assemblyName: OwnMatch3
    namespaceName: DevsDaddy.GameShield.Demo.UI
    className: WelcomeView
  - hash:
      serializedVersion: 2
      Hash: cb3e99cf77a5a54b538ab9732df46555
    assemblyName: OwnMatch3
    namespaceName: 
    className: UpdateGame
  - hash:
      serializedVersion: 2
      Hash: 4bb8dde37a6865c0f9a229c9eb9a6cc9
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXInputAxisBinder
  - hash:
      serializedVersion: 2
      Hash: d75df0b83c74ab3d9088964ef8188507
    assemblyName: Unity.2D.Animation.Runtime
    namespaceName: UnityEngine.U2D.Animation
    className: SpriteSkin
  - hash:
      serializedVersion: 2
      Hash: 5d8969a714dea41f829324ad858dc2d1
    assemblyName: Unity.LevelPlay
    namespaceName: Unity.Services.LevelPlay
    className: RewardedPrefab
  - hash:
      serializedVersion: 2
      Hash: 50beb4402767221f3a35ff91b2058797
    assemblyName: Google.Play.Common
    namespaceName: Google.Play.Common.LoadingScreen
    className: ScrollingFillAnimator
  - hash:
      serializedVersion: 2
      Hash: e4b7831f0a9e91f079eb40ea24427631
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ColorAdjustments
  - hash:
      serializedVersion: 2
      Hash: 14f8c3da54a840e4c398ed3fa7b6057f
    assemblyName: Unity.2D.SpriteShape.Runtime
    namespaceName: 
    className: SpriteShapeGeometryCache
  - hash:
      serializedVersion: 2
      Hash: 96e7bcfd2d072992dab64ac44058b930
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCancelMessageListener
  - hash:
      serializedVersion: 2
      Hash: 415316b9bbc4e339c901a46503e2790b
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: VolumeProfile
  - hash:
      serializedVersion: 2
      Hash: e890b43719831e22ff57acc81dc24437
    assemblyName: OwnMatch3
    namespaceName: 
    className: BonusGemSystemExampleAdvanced
  - hash:
      serializedVersion: 2
      Hash: 467f4a5e5f0a57c9f502e4d66c72baf5
    assemblyName: Unity.2D.Animation.Runtime
    namespaceName: UnityEngine.U2D.Animation
    className: SpriteSkinContainer
  - hash:
      serializedVersion: 2
      Hash: 4493019c8a3af785d6842d09620db209
    assemblyName: Assembly-CSharp
    namespaceName: Match3
    className: BonusGemBonusItem
  - hash:
      serializedVersion: 2
      Hash: 2c1a4031ff14a1553f3c9b5609b5e1d6
    assemblyName: OwnMatch3
    namespaceName: OwnMatch3.Boosters
    className: BoosterManager
  - hash:
      serializedVersion: 2
      Hash: 5f27242e1ad2aadb1faa604dd779a464
    assemblyName: Unity.2D.Animation.Runtime
    namespaceName: UnityEngine.U2D.Animation
    className: SpriteLibrary
  - hash:
      serializedVersion: 2
      Hash: 5a4a30afca066f6c16cf63c3ae02c977
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: UniversalAdditionalLightData
  - hash:
      serializedVersion: 2
      Hash: 24af424a437762b0c98a2238a41b2825
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerButton
  - hash:
      serializedVersion: 2
      Hash: ff76cf6cf52b4db7b95af6ffadca9288
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: SignalEmitter
  - hash:
      serializedVersion: 2
      Hash: 0f19765290d81f9e93eff7828a091d40
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: PlayerInput
  - hash:
      serializedVersion: 2
      Hash: 278b1ac154192a2d8342f76e952edce2
    assemblyName: OwnMatch3
    namespaceName: 
    className: GoalDisplayConfiguration
  - hash:
      serializedVersion: 2
      Hash: b008e4bd84a7749366eb55cfe66bd3f8
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: FPSMeter
  - hash:
      serializedVersion: 2
      Hash: d7c8092511063e0dae8106bfc8ed63dd
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnScrollMessageListener
  - hash:
      serializedVersion: 2
      Hash: 3594d8e9fcbd74e51eec80efbf16c599
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: LensDistortion
  - hash:
      serializedVersion: 2
      Hash: 18918a950362d126be2619c889d13b39
    assemblyName: OwnMatch3
    namespaceName: 
    className: TestingSecuredTypes
  - hash:
      serializedVersion: 2
      Hash: 0b63f6d3335e11939ce8790b904c8691
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: ActivationPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: ec6cf673540b2d4e42c612611eeafe4d
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerStayMessageListener
  - hash:
      serializedVersion: 2
      Hash: 6cac290ffa8e0f32236389de6afb8d7d
    assemblyName: CFXRDemo
    namespaceName: CartoonFX
    className: CFXR_Demo_Rotate
  - hash:
      serializedVersion: 2
      Hash: ff5172bc81dc2da4c7de2a8bf56dc4b8
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionsEditorWindow
  - hash:
      serializedVersion: 2
      Hash: b142042ad54bf35a3e6ba47562e59dc8
    assemblyName: PrimeTween.Runtime
    namespaceName: PrimeTween
    className: PrimeTweenManager
  - hash:
      serializedVersion: 2
      Hash: 64756cee21909a7a459d5e05844baa97
    assemblyName: Unity.RenderPipelines.Universal.2D.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: CompositeShadowCaster2D
  - hash:
      serializedVersion: 2
      Hash: e2c57c9f76aa1449b924885e2bda367d
    assemblyName: OwnMatch3
    namespaceName: 
    className: CloudSaveExample
  - hash:
      serializedVersion: 2
      Hash: 8f60f2b52cfa5f9146ac0cce68970788
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXPlaneBinder
  - hash:
      serializedVersion: 2
      Hash: a5f52e289070cca28d53aa2d26bf266a
    assemblyName: Unity.2D.Animation.Runtime
    namespaceName: UnityEngine.U2D.Animation
    className: Bone
  - hash:
      serializedVersion: 2
      Hash: d4b4dd3604c1246131c02d7d57ab1a2d
    assemblyName: OwnMatch3
    namespaceName: 
    className: BonusGemManager
  - hash:
      serializedVersion: 2
      Hash: 4e91ec78a0b85c15f5829fc61195dd97
    assemblyName: OwnMatch3
    namespaceName: 
    className: GameUI
  - hash:
      serializedVersion: 2
      Hash: 2f8921673723123ad9922da7e6cf7628
    assemblyName: Unity.AppUI.Navigation
    namespaceName: Unity.AppUI.Navigation
    className: NavGraphViewHierarchicalNode
  - hash:
      serializedVersion: 2
      Hash: af770ad771658627851c64eafbf797f0
    assemblyName: Unity.2D.Tilemap.Extras
    namespaceName: UnityEngine.Tilemaps
    className: AnimatedTile
  - hash:
      serializedVersion: 2
      Hash: 8fe6d4f095ce838f5396d1867140a9b6
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: CameraSwitcher
  - hash:
      serializedVersion: 2
      Hash: 71f2cfc7883709b7e3588380add4cc80
    assemblyName: OwnMatch3
    namespaceName: DevsDaddy.GameShield.Demo.Controllers
    className: EnemyController
  - hash:
      serializedVersion: 2
      Hash: 4663ce278bf334bc7cb334ea8061129e
    assemblyName: Unity.AI.Navigation
    namespaceName: Unity.AI.Navigation
    className: NavMeshModifierVolume
  - hash:
      serializedVersion: 2
      Hash: 1dea0673a98552ce122688502bf9bde6
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ZStringExample
  - hash:
      serializedVersion: 2
      Hash: b95ed80f1668b1de6acf1ffbee100da1
    assemblyName: OwnMatch3
    namespaceName: 
    className: RocketExplosionEffect
  - hash:
      serializedVersion: 2
      Hash: d497e277ee09923aa44c51901a8f68f9
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: LensFlareDataSRP
  - hash:
      serializedVersion: 2
      Hash: 0a4ef3c4e73b2f3eef913e2325924084
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: InputSettings
  - hash:
      serializedVersion: 2
      Hash: d0392b7f3f52d5af537ade186b4f51f1
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: ControlTrack
  - hash:
      serializedVersion: 2
      Hash: ef5e5ad2d15185cd85d79d7a991a7421
    assemblyName: OwnMatch3
    namespaceName: 
    className: CurrencyAttractorUI
  - hash:
      serializedVersion: 2
      Hash: 360d3bc2983b155d4ce0473cf344b4e5
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXUISliderBinder
  - hash:
      serializedVersion: 2
      Hash: f1e2e1532161f0d4682a921afbb244e6
    assemblyName: Assembly-CSharp
    namespaceName: Match3
    className: FrozenIce
  - hash:
      serializedVersion: 2
      Hash: e9db29e31ba60222fa1690a9445f34f5
    assemblyName: Unity.2D.Tilemap.Extras
    namespaceName: UnityEngine.Tilemaps
    className: RuleOverrideTile
  - hash:
      serializedVersion: 2
      Hash: 62012986306eac8574ad8fba6a362e1c
    assemblyName: OwnMatch3
    namespaceName: OwnMatch3.Avatars
    className: AvatarManager
  - hash:
      serializedVersion: 2
      Hash: b810dfd26c061161a980f9a956f9a116
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ScreenSpaceShadows
  - hash:
      serializedVersion: 2
      Hash: 9dc274eab7228631e90d4757b13b768a
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ShadowsMidtonesHighlights
  - hash:
      serializedVersion: 2
      Hash: 3645b97da6b9d55f246a57b0a5e2e8b7
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXLightBinder
  - hash:
      serializedVersion: 2
      Hash: 049f6aa84fe1cb5359c9338e96d0b07e
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: UIFoldout
  - hash:
      serializedVersion: 2
      Hash: a5ca207a04cc0057994c155b3a40c27f
    assemblyName: OwnMatch3
    namespaceName: 
    className: CloudSaveStatusUI
  - hash:
      serializedVersion: 2
      Hash: 8bbce9932f67ad8e7f4eefec87800f94
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TextContainer
  - hash:
      serializedVersion: 2
      Hash: afc3591da6be8f8c1e7f740e4b98d3a2
    assemblyName: OwnMatch3
    namespaceName: DevsDaddy.GameShield.Demo
    className: GameInstaller
  - hash:
      serializedVersion: 2
      Hash: a74f125c6abd95cef7ecf4ffbbdb89fa
    assemblyName: Google.Play.Common
    namespaceName: Google.Play.Common.LoadingScreen
    className: LoadingBar
  - hash:
      serializedVersion: 2
      Hash: a942f6e9416714c7eeefbdebe8566499
    assemblyName: OwnMatch3
    namespaceName: 
    className: MoveToNextScene
  - hash:
      serializedVersion: 2
      Hash: e1566e58ef86ed2d381d9d2acbb9ac24
    assemblyName: Assembly-CSharp
    namespaceName: Match3
    className: GemSpawner
  - hash:
      serializedVersion: 2
      Hash: 745c234e274606053ecac394bbce2be6
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXUIDropdownBinder
  - hash:
      serializedVersion: 2
      Hash: ad49e20f31ed572e586aa8bbaafb591b
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnDropMessageListener
  - hash:
      serializedVersion: 2
      Hash: 04b6685b6f724c0d6024e6b6f96c8202
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: TimelineAsset
  - hash:
      serializedVersion: 2
      Hash: 98bba821de91f307ad7bd5e1c0a8fa3f
    assemblyName: OwnMatch3
    namespaceName: 
    className: LevelSetup
  - hash:
      serializedVersion: 2
      Hash: 6d394de691f6d0187f61c08889353d0e
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnButtonClickMessageListener
  - hash:
      serializedVersion: 2
      Hash: b98001682cba83786775c9e415b89a61
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnDropdownValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: b838cbada0b03f1cfbaebc8e124f4f39
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.OnScreen
    className: OnScreenButton
  - hash:
      serializedVersion: 2
      Hash: bb8d968ef4474e71aaad4a4dce279f90
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseDownMessageListener
  - hash:
      serializedVersion: 2
      Hash: 31655e10ee5b82efba63a131a0fda6f6
    assemblyName: OwnMatch3
    namespaceName: 
    className: AIGenerationConfig
  - hash:
      serializedVersion: 2
      Hash: 7e9cad7f71f9cc52f699bbd2d2a75734
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerClickMessageListener
  - hash:
      serializedVersion: 2
      Hash: 51ddae688ace3c944025f1af60ba854b
    assemblyName: OwnMatch3
    namespaceName: OwnMatch3.UI.Particles
    className: ParticleElementDemo
  - hash:
      serializedVersion: 2
      Hash: 5f70b16cd9acc9c1dd88b51b6ed89669
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerIndirectToggle
  - hash:
      serializedVersion: 2
      Hash: 5874b789cd9832847c61ebfa803213af
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UIElements
    className: PanelEventHandler
  - hash:
      serializedVersion: 2
      Hash: 656900f3eacc1cd944adb71a70bd9396
    assemblyName: Unity.RenderPipelines.Universal.2D.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: PixelPerfectCamera
  - hash:
      serializedVersion: 2
      Hash: c37cd72545fef3582dfd3ca62cebb73f
    assemblyName: OwnMatch3
    namespaceName: 
    className: GameProgressManagerEditor
  - hash:
      serializedVersion: 2
      Hash: d771cabeaa363771c54f6c41824f7abb
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: LiftGammaGain
  - hash:
      serializedVersion: 2
      Hash: d646c9c8280ddb8137ccff79e453f62d
    assemblyName: OwnMatch3
    namespaceName: DevsDaddy.GameShield.Demo.Controllers
    className: CameraController
  - hash:
      serializedVersion: 2
      Hash: cb49ce80d4b69cb2ed30d83bfcb146ae
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerBitField
  - hash:
      serializedVersion: 2
      Hash: 1e6aa1f905f62bcd0558226c29920412
    assemblyName: Unity.2D.SpriteShape.Runtime
    namespaceName: UnityEngine.U2D
    className: SpriteShapeObjectPlacement
  - hash:
      serializedVersion: 2
      Hash: d2ecd94550edf1b56aff07b1f4e41c57
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerEnter2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 5ebc58ec194dd44630b25fcc270ad4af
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Dropdown
  - hash:
      serializedVersion: 2
      Hash: f9e62f3a7d9c2e2748f7fb68babb8bea
    assemblyName: CFXRRuntime
    namespaceName: CartoonFX
    className: CFXR_EmissionBySurface
  - hash:
      serializedVersion: 2
      Hash: 2cc5af0f17cef9c717df88c8c7e4c9aa
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine
    className: LightAnchor
  - hash:
      serializedVersion: 2
      Hash: 01505eb5fdfa1c5ddd7e3095eb897fda
    assemblyName: OwnMatch3
    namespaceName: 
    className: FPSCounter
  - hash:
      serializedVersion: 2
      Hash: fb3821ad9a52b2bb1d3862680727b26c
    assemblyName: Assembly-CSharp
    namespaceName: Match3
    className: Board
  - hash:
      serializedVersion: 2
      Hash: 3cf03318ee34585901419f89503587ba
    assemblyName: Assembly-CSharp
    namespaceName: Match3
    className: ShopItemCoin
  - hash:
      serializedVersion: 2
      Hash: 607a3312b21ae0c1bc54e1f9d55c5101
    assemblyName: Unity.AppUI.Navigation
    namespaceName: Unity.AppUI.Navigation
    className: NavGraphViewAsset
  - hash:
      serializedVersion: 2
      Hash: 17a71904793c41964b29cf2c93c6e006
    assemblyName: Unity.2D.IK.Runtime
    namespaceName: UnityEngine.U2D.IK
    className: IKManager2D
  - hash:
      serializedVersion: 2
      Hash: 5d3245aa0c305863cda4adfbb2c6df23
    assemblyName: Assembly-CSharp
    namespaceName: Match3
    className: ColorClean
  - hash:
      serializedVersion: 2
      Hash: 643dc957fef4cafbb6489f5c3c2474e0
    assemblyName: OwnMatch3
    namespaceName: DevsDaddy.GameShield.Demo.UI
    className: ReportingView
  - hash:
      serializedVersion: 2
      Hash: df441c9d5c06f713d281fc9efcb13527
    assemblyName: Unity.LevelPlay
    namespaceName: 
    className: IronSourceEvents
  - hash:
      serializedVersion: 2
      Hash: 0a79dbd3552c8271f441d7dbc40502f1
    assemblyName: OwnMatch3
    namespaceName: 
    className: Bonus
  - hash:
      serializedVersion: 2
      Hash: 5dc51b0140815ee83ba613dce32f5b12
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerPersistentCanvas
  - hash:
      serializedVersion: 2
      Hash: bc7ddad761f2f6564f77dd206d2e31cc
    assemblyName: OwnMatch3
    namespaceName: 
    className: EndGamePopupController
  - hash:
      serializedVersion: 2
      Hash: 33452e73490fa8fab9a95cbb8c2b2de0
    assemblyName: Assembly-CSharp
    namespaceName: Match3
    className: UIHandler
  - hash:
      serializedVersion: 2
      Hash: 181f45a2448c21f719cf20c8c06067b1
    assemblyName: OwnMatch3
    namespaceName: 
    className: CurrencyAnimationManager
  - hash:
      serializedVersion: 2
      Hash: b5007673bbbdb9d12597a1d8201370f5
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Text
  - hash:
      serializedVersion: 2
      Hash: bfd4af30d9804c73a547e494b337b534
    assemblyName: Unity.2D.Tilemap.Extras
    namespaceName: UnityEngine
    className: RuleTile
  - hash:
      serializedVersion: 2
      Hash: 607abb3f6d98d784ee5776df75cc44cb
    assemblyName: Assembly-CSharp
    namespaceName: Match3
    className: JumpingFish
  - hash:
      serializedVersion: 2
      Hash: a6d4760c1aaaf3fc6e618d120234abd0
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX
    className: SpawnOverDistance
  - hash:
      serializedVersion: 2
      Hash: d5cd7f207e91ca8336c65a577a6fb2a0
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: ScrollRect
  - hash:
      serializedVersion: 2
      Hash: f45600bcc886206dd55a3208474aad61
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnScrollbarValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 00bbd9db8634496d079d17711b2ff7c4
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Selectable
  - hash:
      serializedVersion: 2
      Hash: 0ed88db05f5e21b9b9292eaa1e1d1bed
    assemblyName: OwnMatch3
    namespaceName: 
    className: LevelCountTest
  - hash:
      serializedVersion: 2
      Hash: fce9b835d1e8b08e7ecea8da19f21986
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: CoroutineRunner
  - hash:
      serializedVersion: 2
      Hash: 0f723e8457ba62c9101c2f7a507a3867
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: 
    className: VisualEffectActivationClip
  - hash:
      serializedVersion: 2
      Hash: 51603d25dc8c39903df98901361f0b73
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: DecalRendererFeature
  - hash:
      serializedVersion: 2
      Hash: 6217b1287eca7eed313cf0b864249a30
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionExit2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: fb79e95da8891fc7ac6c36ca6967af23
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: ActivationTrack
  - hash:
      serializedVersion: 2
      Hash: 552dca338f20933ce362ebbe559a6aa6
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 87d757cf8ee56a6fbafd78ea693f650b
    assemblyName: OwnMatch3
    namespaceName: 
    className: VideoFrameExtractorWindow
  - hash:
      serializedVersion: 2
      Hash: 4a011f43e86328b5ac7475de7868f9b1
    assemblyName: OwnMatch3
    namespaceName: OwnMatch3.Shop
    className: ShopConfiguration
  - hash:
      serializedVersion: 2
      Hash: 191cd500c627cd6682ef6f70b8e47f2c
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionEnter2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 3d0c716370eedd027da2b981a83deeed
    assemblyName: Assembly-CSharp
    namespaceName: Match3
    className: LineRocket
  - hash:
      serializedVersion: 2
      Hash: ef38c1407dba8c1f5d69f5db61a694fe
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: AnimatorMessageListener
  - hash:
      serializedVersion: 2
      Hash: ceda9fb5e1ebe6fdb3664bc9e65d94c2
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ZStringGameExamples
  - hash:
      serializedVersion: 2
      Hash: b055b57629839af049e1c44453e641a8
    assemblyName: OwnMatch3
    namespaceName: OwnMatch3.UI.Particles
    className: ParticleElementSizeTest
  - hash:
      serializedVersion: 2
      Hash: 711fcd430ced52d118771c9c71e13238
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: RenderObjects
  - hash:
      serializedVersion: 2
      Hash: b12640dfdb5966813e9dd838893e6c4a
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: 8d5318ed8488b1fe17c08044ce1b8309
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_StyleSheet
  - hash:
      serializedVersion: 2
      Hash: e18dfd2b913b09fbbb2405165a2e6a44
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_Dropdown
  - hash:
      serializedVersion: 2
      Hash: ada873da50974d1e7ce247ffb296b0f3
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: 5382156409d7c462c67822a5ee8d6c85
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerToggleHistory
  - hash:
      serializedVersion: 2
      Hash: d85292b1d7ddb10e5a2df5d221dcba86
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: InputActionAsset
  - hash:
      serializedVersion: 2
      Hash: 185e7bacfbbc5defefa609c70f7ff4ce
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_ColorGradient
  - hash:
      serializedVersion: 2
      Hash: e402a382f213255c22bda9254dd831b3
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: SceneVariables
  - hash:
      serializedVersion: 2
      Hash: 3e043cedd3ead1005a56f5ba7d3df841
    assemblyName: Unity.LevelPlay
    namespaceName: 
    className: IronSourceEventsDispatcher
  - hash:
      serializedVersion: 2
      Hash: a4ba242ad7c19ed77c775b388363e75c
    assemblyName: Unity.AI.Navigation
    namespaceName: Unity.AI.Navigation
    className: NavMeshLink
  - hash:
      serializedVersion: 2
      Hash: fea660240dde52e08bd79d154e104e15
    assemblyName: Unity.RenderPipelines.Universal.2D.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: Light2D
  - hash:
      serializedVersion: 2
      Hash: 10399b4e44f874d68a2dad01fa7b8c30
    assemblyName: Unity.AI.Navigation
    namespaceName: Unity.AI.Navigation
    className: NavMeshSurface
  - hash:
      serializedVersion: 2
      Hash: 540882762544409cc4530e60db00e951
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: CanvasScaler
  - hash:
      serializedVersion: 2
      Hash: 88ecff4820ba229645587fd73a97f3a9
    assemblyName: Unity.AppUI
    namespaceName: Unity.AppUI.Core
    className: AppUISettings
  - hash:
      serializedVersion: 2
      Hash: 17142b03663387290480c82303274c02
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: 00eef92bf387da2c9009267123d9d674
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerExit2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 9f4b4dfe9a06bd18688a63fa04f780af
    assemblyName: OwnMatch3
    namespaceName: 
    className: ObjectPoolerMonitor
  - hash:
      serializedVersion: 2
      Hash: ed2c59b75f8f3cd8df4cd4387d965e71
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerVector2
  - hash:
      serializedVersion: 2
      Hash: 607f6f7bf35289362164ffb4a6254b64
    assemblyName: Assembly-CSharp
    namespaceName: Match3
    className: SmallBomb
  - hash:
      serializedVersion: 2
      Hash: 2541c03d117382d4ebbe0bb0775dd2dc
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerProgressBar
  - hash:
      serializedVersion: 2
      Hash: f33b95fe9aa5580c1ff792b8447cc04e
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXUIToggleBinder
  - hash:
      serializedVersion: 2
      Hash: 5c21f4846181ee6e245db1fcfb62fc0e
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerContainer
  - hash:
      serializedVersion: 2
      Hash: ca7965796a6fc0e5baad4cb19f5b5ded
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnJointBreak2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 000b9e9247892bc6558c436b8419c144
    assemblyName: OwnMatch3
    namespaceName: 
    className: BonusActivityTracker
  - hash:
      serializedVersion: 2
      Hash: cde402e184a6ab530144506bc9d6a0af
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerVector4
  - hash:
      serializedVersion: 2
      Hash: 3619bbe901cd3d551eb5ea7cc6882b89
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXAudioSpectrumBinder
  - hash:
      serializedVersion: 2
      Hash: 3ca7f2be93d5156a91693dfd4bd38e3f
    assemblyName: Unity.2D.IK.Runtime
    namespaceName: UnityEngine.U2D.IK
    className: FabrikSolver2D
  - hash:
      serializedVersion: 2
      Hash: 0814f85034902f3734f70fbf99b24c5c
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXRaycastBinder
  - hash:
      serializedVersion: 2
      Hash: f778e23b6ff26b8de4721be0eb1fa240
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionStayMessageListener
  - hash:
      serializedVersion: 2
      Hash: 6a3fe9cd5c249e54d1316ae7a2e9fe71
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: LevelPlaySample
  - hash:
      serializedVersion: 2
      Hash: 84369b0a9770c5e4459fe445e35db5ee
    assemblyName: Google.Play.Core
    namespaceName: Google.Play.Core.Internal
    className: PlayCoreEventHandler
  - hash:
      serializedVersion: 2
      Hash: 27fcf46f05da57849d306826fb32eb18
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: SplitToning
  - hash:
      serializedVersion: 2
      Hash: b747f5d40674055dd70e18552f1b0447
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Mask
  - hash:
      serializedVersion: 2
      Hash: e6b258489c88376c5869946b2735e7e5
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_FontAsset
  - hash:
      serializedVersion: 2
      Hash: 1e0436e0129778a6673a7f15e11e67c1
    assemblyName: OwnMatch3
    namespaceName: Match3
    className: ObstacleSoundSystemSetup
  - hash:
      serializedVersion: 2
      Hash: 8529e07129dbcadf3112753a46e3ca82
    assemblyName: OwnMatch3
    namespaceName: OwnMatch3.Avatars
    className: AvatarConfiguration
  - hash:
      serializedVersion: 2
      Hash: 3717ec89171ae7821f76d3549825ce1b
    assemblyName: Unity.InferenceEngine
    namespaceName: Unity.InferenceEngine
    className: ModelAssetData
  - hash:
      serializedVersion: 2
      Hash: 6a0b5b415c00f783202fd1ae2d556cb2
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: AudioPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: 6f248028b3497460b25044538dde24ca
    assemblyName: Google.Play.Games
    namespaceName: GooglePlayGames.OurUtils
    className: PlayGamesHelperObject
  - hash:
      serializedVersion: 2
      Hash: debd75e86f6ad9e505030da81f8ca441
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX
    className: VisualEffectControlTrack
  - hash:
      serializedVersion: 2
      Hash: d33a961b5cbfca20d7b8d811d15fb4da
    assemblyName: OwnMatch3
    namespaceName: DevsDaddy.GameShield.Demo.UI
    className: CaptchaView
  - hash:
      serializedVersion: 2
      Hash: 72269a5e2a0c34486d67f955498a6d89
    assemblyName: CFXRDemo
    namespaceName: CartoonFX
    className: CFXR_Demo
  - hash:
      serializedVersion: 2
      Hash: b88e78b6860b6061b2c681ea9b80b6bf
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerToggle
  - hash:
      serializedVersion: 2
      Hash: db6133687572f9dc24f1fd4238ef9b83
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TextMeshPro
  - hash:
      serializedVersion: 2
      Hash: 4bbce6c4d13cfd78ce48f10ae797c014
    assemblyName: Unity.AppUI.Navigation
    namespaceName: Unity.AppUI.Navigation
    className: NavDestination
  - hash:
      serializedVersion: 2
      Hash: 10c17b309bdca62b4d7fbaf113ed4ca0
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Toggle
  - hash:
      serializedVersion: 2
      Hash: 58f6c91dc9977217e7536afccdb74f71
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: ProbeAdjustmentVolume
  - hash:
      serializedVersion: 2
      Hash: 75a64e5c73e8796187bfb012035f803d
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX.Utility
    className: VFXPreviousPositionBinder
  - hash:
      serializedVersion: 2
      Hash: 61268ab592453a893888fb08f8428c3f
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX
    className: LoopAndDelay
  - hash:
      serializedVersion: 2
      Hash: 9a174471d4db2c0c6d3ac47d2f818db1
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnParticleCollisionMessageListener
  - hash:
      serializedVersion: 2
      Hash: 497b78588d7714a71c9ad3bcefb11a90
    assemblyName: Unity.LevelPlay
    namespaceName: Unity.Services.LevelPlay
    className: AdPrefab
  - hash:
      serializedVersion: 2
      Hash: 83cfda82082c233d96cc677c19f57e07
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnJointBreakMessageListener
  - hash:
      serializedVersion: 2
      Hash: a1ac4c50e55874de16eee3db71aa9784
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: ToggleGroup
  - hash:
      serializedVersion: 2
      Hash: 3d0680dd4df6a6b0f252285e8ba72ced
    assemblyName: OwnMatch3
    namespaceName: 
    className: AILevelGenerator
  - hash:
      serializedVersion: 2
      Hash: 6478d2d26af607a418623e4c8dbce3c6
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: FullScreenPassRendererFeature
  - hash:
      serializedVersion: 2
      Hash: 4718916586b3818eb69bf65df61f0f3a
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionStay2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 5afb43d3a528f755a21ed42a793915f4
    assemblyName: Unity.RenderPipelines.Universal.2D.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: Renderer2DData
  - hash:
      serializedVersion: 2
      Hash: d82086405f46de14829f6b4bcbdd8fc9
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnBecameInvisibleMessageListener
  - hash:
      serializedVersion: 2
      Hash: 5bb0d98fe0b0db302eaa611b101e76c9
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionEditorWindow
  - hash:
      serializedVersion: 2
      Hash: ba331fc935a6a1327554a41b7ff164fe
    assemblyName: Unity.VisualEffectGraph.Runtime
    namespaceName: UnityEngine.VFX
    className: VFXRuntimeResources
  - hash:
      serializedVersion: 2
      Hash: f2305da2400dfe6f069770a1a63f0f7c
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: PlayableTrack
  - hash:
      serializedVersion: 2
      Hash: 7b630d5840289e44edc951bb947a4a81
    assemblyName: OwnMatch3
    namespaceName: 
    className: EncryptionKeyGeneratorWindow
  platform: 13
  scenePathNames:
  - Assets/OwnMatch3/DecreaseScene.unity
  - Assets/OwnMatch3/Scripts/OwnMatch.unity
  playerPath: F:/Match2D/Match3.aab
