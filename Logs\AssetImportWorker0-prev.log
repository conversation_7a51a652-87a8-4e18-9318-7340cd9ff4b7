Using pre-set license
Built from '6000.2/staging' branch; Version is '6000.2.0b9 (377f5a9787ef) revision 3637082'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 32596 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-07-25T15:49:47Z

COMMAND LINE ARGUMENTS:
F:\Unity Installs\6000.2.0b9\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
F:/Match2D
-logFile
Logs/AssetImportWorker0.log
-srvPort
11052
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: F:/Match2D
F:/Match2D
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [32380]  Target information:

Player connection [32380]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2919582002 [EditorId] 2919582002 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-8GO5TD1) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [32380] Host joined multi-casting on [***********:54997]...
Player connection [32380] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 16.34 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 1.26 ms.
Initialize engine version: 6000.2.0b9 (377f5a9787ef)
[Subsystems] Discovering subsystems at path F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/Match2D/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1660 SUPER (ID=0x21c4)
    Vendor:   NVIDIA
    VRAM:     5966 MB
    Driver:   32.0.15.7657
Initialize mono
Mono path[0] = 'F:/Unity Installs/6000.2.0b9/Editor/Data/Managed'
Mono path[1] = 'F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56640
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.001128 seconds.
- Loaded All Assemblies, in  0.373 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 216 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.645 seconds
Domain Reload Profiling: 1018ms
	BeginReloadAssembly (114ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (169ms)
		LoadAssemblies (114ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (165ms)
			TypeCache.Refresh (164ms)
				TypeCache.ScanAssembly (152ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (646ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (569ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (338ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (65ms)
			ProcessInitializeOnLoadAttributes (112ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.001 seconds
Refreshing native plugins compatible for Editor in 5.38 ms, found 9 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
GPGSUpgrader start
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:38)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 38)

GPGSUpgrader done
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:57)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 57)

Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <e887908fdf9b4028bc879f0c1572eba4>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Refreshing native plugins compatible for Editor in 5.43 ms, found 9 plugins.
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.162 seconds
Domain Reload Profiling: 2161ms
	BeginReloadAssembly (163ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (751ms)
		LoadAssemblies (404ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (433ms)
			TypeCache.Refresh (311ms)
				TypeCache.ScanAssembly (283ms)
			BuildScriptInfoCaches (102ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1162ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (994ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (183ms)
			ProcessInitializeOnLoadAttributes (514ms)
			ProcessInitializeOnLoadMethodAttributes (282ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 9.33 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.09 ms.
Unloading 70 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10471 unused Assets / (22.8 MB). Loaded Objects now: 13180.
Memory consumption went from 288.0 MB to 265.3 MB.
Total: 19.715300 ms (FindLiveObjects: 1.160300 ms CreateObjectMapping: 1.325900 ms MarkObjects: 8.751100 ms  DeleteObjects: 8.476300 ms)

========================================================================
Received Import Request.
  Time since last request: 21463.716025 seconds.
  path: Assets/OwnMatch3/Scripts/Examples/StarAnimationTester.cs
  artifactKey: Guid(69d51779d9d100c49ada41020557a31e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/OwnMatch3/Scripts/Examples/StarAnimationTester.cs using Guid(69d51779d9d100c49ada41020557a31e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eee9106f13ba66929b9ffa3e7b2edc5f') in 0.2907403 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 20.59 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.10 ms.
Unloading 64 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10378 unused Assets / (21.5 MB). Loaded Objects now: 13182.
Memory consumption went from 272.5 MB to 251.0 MB.
Total: 33.347900 ms (FindLiveObjects: 5.746800 ms CreateObjectMapping: 4.553400 ms MarkObjects: 12.128400 ms  DeleteObjects: 10.917300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 407.585834 seconds.
  path: Assets/OwnMatch3/Scripts/Utils/CameraManager.cs
  artifactKey: Guid(e1f8deee91776f043a53ab83ec56a452) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/OwnMatch3/Scripts/Utils/CameraManager.cs using Guid(e1f8deee91776f043a53ab83ec56a452) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '957d411d606fe40c15b79934e9b02f08') in 0.0228655 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 9.67 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.09 ms.
Unloading 64 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10378 unused Assets / (21.8 MB). Loaded Objects now: 13182.
Memory consumption went from 272.5 MB to 250.7 MB.
Total: 19.299300 ms (FindLiveObjects: 1.223300 ms CreateObjectMapping: 1.540000 ms MarkObjects: 8.361700 ms  DeleteObjects: 8.172400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 124.133437 seconds.
  path: Assets/OwnMatch3/Scripts/Utils/CameraManager.cs
  artifactKey: Guid(e1f8deee91776f043a53ab83ec56a452) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/OwnMatch3/Scripts/Utils/CameraManager.cs using Guid(e1f8deee91776f043a53ab83ec56a452) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2f0a5fd7963b3119f8cba7d697517239') in 0.0153766 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 37.43 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.19 ms.
Unloading 64 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10378 unused Assets / (23.8 MB). Loaded Objects now: 13182.
Memory consumption went from 272.5 MB to 248.7 MB.
Total: 36.699800 ms (FindLiveObjects: 2.443300 ms CreateObjectMapping: 3.939200 ms MarkObjects: 19.592100 ms  DeleteObjects: 10.723500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 11.33 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.09 ms.
Unloading 64 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10378 unused Assets / (22.8 MB). Loaded Objects now: 13182.
Memory consumption went from 272.5 MB to 249.6 MB.
Total: 22.432500 ms (FindLiveObjects: 1.543600 ms CreateObjectMapping: 1.765200 ms MarkObjects: 8.545500 ms  DeleteObjects: 10.576400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 136.472539 seconds.
  path: Assets/OwnMatch3/Scripts/Utils/CameraManager.cs
  artifactKey: Guid(e1f8deee91776f043a53ab83ec56a452) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/OwnMatch3/Scripts/Utils/CameraManager.cs using Guid(e1f8deee91776f043a53ab83ec56a452) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '34695114cc63f1c1171a341f56697e6a') in 0.0203467 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 9.38 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.10 ms.
Unloading 64 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10378 unused Assets / (22.1 MB). Loaded Objects now: 13182.
Memory consumption went from 272.5 MB to 250.4 MB.
Total: 25.436000 ms (FindLiveObjects: 1.403000 ms CreateObjectMapping: 2.238400 ms MarkObjects: 12.801500 ms  DeleteObjects: 8.990700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 405.676070 seconds.
  path: Assets/OwnMatch3/Scripts/Utils/CameraManager.cs
  artifactKey: Guid(e1f8deee91776f043a53ab83ec56a452) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/OwnMatch3/Scripts/Utils/CameraManager.cs using Guid(e1f8deee91776f043a53ab83ec56a452) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '28bc77b153bed627ebb9d9533bcb344e') in 0.0244675 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 13.96 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.10 ms.
Unloading 64 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10378 unused Assets / (22.3 MB). Loaded Objects now: 13182.
Memory consumption went from 272.5 MB to 250.2 MB.
Total: 22.613100 ms (FindLiveObjects: 1.717900 ms CreateObjectMapping: 2.291800 ms MarkObjects: 9.110300 ms  DeleteObjects: 9.491500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 114.756989 seconds.
  path: Assets/OwnMatch3/Scripts/Utils/CameraManager.cs
  artifactKey: Guid(e1f8deee91776f043a53ab83ec56a452) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/OwnMatch3/Scripts/Utils/CameraManager.cs using Guid(e1f8deee91776f043a53ab83ec56a452) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6e14037fc67f92c77ecbe62af83c6b74') in 0.022638 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.168 seconds
Refreshing native plugins compatible for Editor in 9.90 ms, found 9 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
GPGSUpgrader start
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:38)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 38)

GPGSUpgrader done
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:57)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 57)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <e887908fdf9b4028bc879f0c1572eba4>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Refreshing native plugins compatible for Editor in 8.96 ms, found 9 plugins.
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.068 seconds
Domain Reload Profiling: 4242ms
	BeginReloadAssembly (295ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (124ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (2814ms)
		LoadAssemblies (2614ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (289ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (256ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1069ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (876ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (211ms)
			ProcessInitializeOnLoadAttributes (457ms)
			ProcessInitializeOnLoadMethodAttributes (192ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 9.40 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.08 ms.
Unloading 64 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10470 unused Assets / (22.7 MB). Loaded Objects now: 13186.
Memory consumption went from 293.1 MB to 270.4 MB.
Total: 20.296100 ms (FindLiveObjects: 1.306700 ms CreateObjectMapping: 1.725000 ms MarkObjects: 8.435500 ms  DeleteObjects: 8.827400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 7523.205865 seconds.
  path: Assets/OwnMatch3/Scripts/Utils/ObjectPooler.cs
  artifactKey: Guid(8e8bdbcf44fe9f544a374927686cfded) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/OwnMatch3/Scripts/Utils/ObjectPooler.cs using Guid(8e8bdbcf44fe9f544a374927686cfded) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'df0591512e2fa12d980efddd150fe191') in 0.1647212 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 11002.059296 seconds.
  path: Assets/OwnMatch3/Scripts/OwnMatch.unity
  artifactKey: Guid(be6d1ff4e0397a4419d3e7ce67bc9953) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/OwnMatch3/Scripts/OwnMatch.unity using Guid(be6d1ff4e0397a4419d3e7ce67bc9953) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9f1d6fbc76c9d40541cff8353ccf4b93') in 0.3822936 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 9.84 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.10 ms.
Unloading 64 Unused Serialized files (Serialized files now loaded: 0)
Unloading 64 unused Assets / (13.0 MB). Loaded Objects now: 13186.
Memory consumption went from 269.7 MB to 256.7 MB.
Total: 19.414800 ms (FindLiveObjects: 1.329600 ms CreateObjectMapping: 0.555300 ms MarkObjects: 14.108300 ms  DeleteObjects: 3.419200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 3216.988688 seconds.
  path: Assets/OwnMatch3/Fonts/Cute Font/Beaver Punch SDF.asset
  artifactKey: Guid(a9fbbf114d41f2b4b994afa7ad065f78) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/OwnMatch3/Fonts/Cute Font/Beaver Punch SDF.asset using Guid(a9fbbf114d41f2b4b994afa7ad065f78) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f8aa9371ab1ebd5cf7c33728121be698') in 0.3065411 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

