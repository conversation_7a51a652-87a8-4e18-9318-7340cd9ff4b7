using PrimeTween;
using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using Cysharp.Threading.Tasks;
using Unity.Collections;
using Unity.Jobs;
using Unity.Burst;
using OwnMatch3.Utils;
using System;

/// <summary>
/// Performance-optimized partial class for Match3Board
/// Addresses frame drops during gem falling and match resolution
/// </summary>
public partial class Match3Board
{
    [Header("Performance Settings")]
    [SerializeField] private int maxConcurrentAnimations = 20;
    [SerializeField] private float animationBatchDelay = 0.02f; // 20ms between batches
    [SerializeField] private bool useAsyncJobs = true;
    [SerializeField] private bool useBatchedAnimations = true;
    
    // Pooled NativeCollections for performance
    private NativeArray<Vector3Int> _pooledColumnPositions;
    private NativeHashMap<Vector3Int, bool> _pooledGemExistsMap;
    private NativeHashMap<Vector3Int, bool> _pooledObstacleBlocksMap;
    private NativeList<GemMove> _pooledMoves;
    private bool _nativeCollectionsInitialized = false;
    
    // Animation batching system
    private readonly List<AnimationBatch> _animationBatches = new List<AnimationBatch>();
    
    private struct AnimationBatch
    {
        public List<GemAnimationData> animations;
        public float delay;
    }
    
    private struct GemAnimationData
    {
        public Transform transform;
        public Vector3 targetPosition;
        public float duration;
        public Ease easeType;
    }
    
    /// <summary>
    /// Initialize pooled native collections for better performance
    /// </summary>
    private void InitializeNativeCollectionPools()
    {
        if (_nativeCollectionsInitialized) return;
        
        int maxBoardSize = validPositions.Count;
        _pooledColumnPositions = new NativeArray<Vector3Int>(maxBoardSize, Allocator.Persistent);
        _pooledGemExistsMap = new NativeHashMap<Vector3Int, bool>(maxBoardSize, Allocator.Persistent);
        _pooledObstacleBlocksMap = new NativeHashMap<Vector3Int, bool>(maxBoardSize, Allocator.Persistent);
        _pooledMoves = new NativeList<GemMove>(maxBoardSize, Allocator.Persistent);
        
        _nativeCollectionsInitialized = true;
    }
    
    /// <summary>
    /// Dispose pooled native collections
    /// </summary>
    private void DisposeNativeCollectionPools()
    {
        if (!_nativeCollectionsInitialized) return;
        
        if (_pooledColumnPositions.IsCreated) _pooledColumnPositions.Dispose();
        if (_pooledGemExistsMap.IsCreated) _pooledGemExistsMap.Dispose();
        if (_pooledObstacleBlocksMap.IsCreated) _pooledObstacleBlocksMap.Dispose();
        if (_pooledMoves.IsCreated) _pooledMoves.Dispose();
        
        _nativeCollectionsInitialized = false;
    }
    
    /// <summary>
    /// Performance-optimized gravity and refill system
    /// </summary>
    public async UniTask<bool> ApplyGravityAndRefillOptimized()
    {
        if (!useBatchedAnimations)
        {
            // Fallback to original method if optimizations are disabled
            return await ApplyGravityAndRefill();
        }
        
        // Hide any active hints since the board is about to change
        OnPlayerActivity();
        
        InitializeNativeCollectionPools();
        CacheColumnData();
        var bounds = GetValidBounds();
        bool wasChanged = false;
        int gemsActuallyMoved = 0;
        
        _animationBatches.Clear();
        var columns = validPositions.GroupBy(p => p.x).ToList();
        
        try
        {
            // --- Phase 1: Calculate gravity moves using async jobs ---
            var allMoves = new List<GemMove>();
            
            if (useAsyncJobs)
            {
                allMoves = await CalculateGravityMovesAsync(columns);
            }
            else
            {
                allMoves = CalculateGravityMovesSync(columns);
            }
            
            // --- Phase 2: Apply moves and create batched animations ---
            if (allMoves.Count > 0)
            {
                CreateBatchedFallAnimations(allMoves);
                wasChanged = true;
                gemsActuallyMoved = allMoves.Count;
            }
            
            // --- Phase 3: Execute batched animations ---
            if (_animationBatches.Count > 0)
            {
                await ExecuteBatchedAnimations();
            }
            
            // --- Phase 4: Handle refill with batched animations ---
            var refillResult = await HandleRefillWithBatching(bounds);
            wasChanged = wasChanged || refillResult.wasChanged;
            
            // --- Phase 5: Emergency validation with batched animations ---
            var validationResult = await HandleEmergencyValidationWithBatching(bounds);
            wasChanged = wasChanged || validationResult.wasChanged;
            
        }
        catch (Exception ex)
        {
            DebugManager.LogMatchError($"[BoardPerformanceOptimizations] Error in ApplyGravityAndRefillOptimized: {ex.Message}");
            // Fallback to original method on error
            return await ApplyGravityAndRefill();
        }
        
        return wasChanged;
    }
    
    /// <summary>
    /// Calculate gravity moves asynchronously to prevent frame drops
    /// </summary>
    private async UniTask<List<GemMove>> CalculateGravityMovesAsync(List<IGrouping<int, Vector3Int>> columns)
    {
        var allMoves = new List<GemMove>();
        var jobHandles = new List<JobHandle>();
        var movesPerColumn = new List<NativeList<GemMove>>();
        
        foreach (var col in columns)
        {
            var colPositions = col.OrderBy(p => p.y).ToList();
            if (colPositions.Count < 2) continue;
            
            // Reuse pooled collections
            _pooledGemExistsMap.Clear();
            _pooledObstacleBlocksMap.Clear();
            _pooledMoves.Clear();
            
            // Populate maps
            foreach (var pos in colPositions)
            {
                if (gems.ContainsKey(pos)) _pooledGemExistsMap.TryAdd(pos, true);
                if (obstacles.ContainsKey(pos) && IsCellBlockedByObstacle(pos)) 
                    _pooledObstacleBlocksMap.TryAdd(pos, true);
            }
            
            // Create job
            var columnMoves = new NativeList<GemMove>(colPositions.Count, Allocator.TempJob);
            movesPerColumn.Add(columnMoves);
            
            var fallJob = new CalculateFallJob
            {
                ColumnPositions = new NativeArray<Vector3Int>(colPositions.ToArray(), Allocator.TempJob),
                GemExistsMap = _pooledGemExistsMap,
                ObstacleBlocksFallMap = _pooledObstacleBlocksMap,
                Moves = columnMoves
            };
            
            jobHandles.Add(fallJob.Schedule());
        }
        
        // Wait for jobs to complete asynchronously
        while (jobHandles.Any(handle => !handle.IsCompleted))
        {
            await UniTask.Yield();
        }
        
        // Complete all jobs and collect results
        foreach (var handle in jobHandles)
        {
            handle.Complete();
        }
        
        foreach (var moves in movesPerColumn)
        {
            foreach (var move in moves)
            {
                allMoves.Add(move);
            }
            moves.Dispose();
        }
        
        return allMoves;
    }
    
    /// <summary>
    /// Synchronous gravity calculation (fallback)
    /// </summary>
    private List<GemMove> CalculateGravityMovesSync(List<IGrouping<int, Vector3Int>> columns)
    {
        var allMoves = new List<GemMove>();
        
        foreach (var col in columns)
        {
            var colPositions = col.OrderBy(p => p.y).ToList();
            if (colPositions.Count < 2) continue;
            
            var columnPositions = new NativeArray<Vector3Int>(colPositions.ToArray(), Allocator.TempJob);
            var gemExistsMap = new NativeHashMap<Vector3Int, bool>(colPositions.Count, Allocator.TempJob);
            var obstacleBlocksMap = new NativeHashMap<Vector3Int, bool>(colPositions.Count, Allocator.TempJob);
            var moves = new NativeList<GemMove>(Allocator.TempJob);
            
            foreach (var pos in colPositions)
            {
                if (gems.ContainsKey(pos)) gemExistsMap.Add(pos, true);
                if (obstacles.ContainsKey(pos) && IsCellBlockedByObstacle(pos)) obstacleBlocksMap.Add(pos, true);
            }
            
            var fallJob = new CalculateFallJob
            {
                ColumnPositions = columnPositions,
                GemExistsMap = gemExistsMap,
                ObstacleBlocksFallMap = obstacleBlocksMap,
                Moves = moves
            };
            
            fallJob.Execute();
            
            foreach (var move in moves)
            {
                allMoves.Add(move);
            }
            
            columnPositions.Dispose();
            gemExistsMap.Dispose();
            obstacleBlocksMap.Dispose();
            moves.Dispose();
        }
        
        return allMoves;
    }
    
    /// <summary>
    /// Create batched animations for falling gems
    /// </summary>
    private void CreateBatchedFallAnimations(List<GemMove> moves)
    {
        var currentBatch = new AnimationBatch
        {
            animations = new List<GemAnimationData>(),
            delay = 0f
        };
        
        int animationsInCurrentBatch = 0;
        
        foreach (var move in moves)
        {
            if (!gems.TryGetValue(move.from, out var gemToMove)) continue;
            if (gems.ContainsKey(move.to)) continue;
            
            // Update gem position in data structure
            RemoveGemWithTracking(move.from, "Optimized Gravity System");
            gems[move.to] = gemToMove;
            gemToMove.CurrentCell = move.to;
            
            // Add to animation batch
            float distance = Vector3Int.Distance(move.from, move.to);
            float duration = distance / fallSpeed;
            
            currentBatch.animations.Add(new GemAnimationData
            {
                transform = gemToMove.transform,
                targetPosition = new Vector3(move.to.x + 0.5f, move.to.y + 0.5f, 0),
                duration = duration,
                easeType = Ease.OutQuad
            });
            
            animationsInCurrentBatch++;
            
            // Start new batch if current one is full
            if (animationsInCurrentBatch >= maxConcurrentAnimations)
            {
                _animationBatches.Add(currentBatch);
                currentBatch = new AnimationBatch
                {
                    animations = new List<GemAnimationData>(),
                    delay = _animationBatches.Count * animationBatchDelay
                };
                animationsInCurrentBatch = 0;
            }
        }
        
        // Add final batch if it has animations
        if (currentBatch.animations.Count > 0)
        {
            _animationBatches.Add(currentBatch);
        }
    }
    
    /// <summary>
    /// Execute batched animations with staggered timing
    /// </summary>
    private async UniTask ExecuteBatchedAnimations()
    {
        var batchTasks = new List<UniTask>();
        
        foreach (var batch in _animationBatches)
        {
            batchTasks.Add(ExecuteAnimationBatch(batch));
        }
        
        await UniTask.WhenAll(batchTasks);
    }
    
    /// <summary>
    /// Execute a single animation batch
    /// </summary>
    private async UniTask ExecuteAnimationBatch(AnimationBatch batch)
    {
        if (batch.delay > 0)
        {
            await UniTask.Delay(TimeSpan.FromSeconds(batch.delay), cancellationToken: cts.Token);
        }
        
        var animationTasks = new List<UniTask>();
        
        foreach (var animation in batch.animations)
        {
            var tween = Tween.Position(animation.transform, animation.targetPosition, 
                animation.duration, animation.easeType);
            animationTasks.Add(tween.ToYieldInstruction().ToUniTask(cancellationToken: cts.Token));
        }
        
        await UniTask.WhenAll(animationTasks);
    }
    
    /// <summary>
    /// Handle refill with batched animations
    /// </summary>
    private async UniTask<(bool wasChanged, int gemsCreated)> HandleRefillWithBatching(BoundsInt bounds)
    {
        bool wasChanged = false;
        int gemsActuallyCreated = 0;
        int startYBase = bounds.yMax + 1;

        // Combine pendingRefill with discovered empty cells
        var allEmptyPositions = new HashSet<Vector3Int>(pendingRefill);

        foreach (var pos in validPositions)
        {
            if (!gems.ContainsKey(pos) && CanGemFallHere(pos))
            {
                allEmptyPositions.Add(pos);
            }
        }

        if (allEmptyPositions.Count == 0)
        {
            pendingRefill.Clear();
            return (wasChanged, gemsActuallyCreated);
        }

        // Group by column for efficient spawning
        var columnGroups = allEmptyPositions.GroupBy(p => p.x).ToList();
        var refillBatches = new List<AnimationBatch>();

        int spawnIndex = 0;
        int currentBatchIndex = 0;

        foreach (var columnGroup in columnGroups)
        {
            var sortedPositions = columnGroup.OrderByDescending(p => p.y).ToList();
            int startY = startYBase + spawnIndex;

            foreach (var pos in sortedPositions)
            {
                var newGem = CreateNewGem(pos, startY);
                gems[pos] = newGem;

                float distance = Mathf.Abs(pos.y - startY);
                float duration = distance / fallSpeed;

                // Add to appropriate batch
                if (currentBatchIndex >= refillBatches.Count)
                {
                    refillBatches.Add(new AnimationBatch
                    {
                        animations = new List<GemAnimationData>(),
                        delay = currentBatchIndex * animationBatchDelay
                    });
                }

                refillBatches[currentBatchIndex].animations.Add(new GemAnimationData
                {
                    transform = newGem.transform,
                    targetPosition = new Vector3(pos.x + 0.5f, pos.y + 0.5f, 0),
                    duration = duration,
                    easeType = Ease.OutBounce
                });

                gemsActuallyCreated++;
                wasChanged = true;
                spawnIndex++;

                // Move to next batch if current one is full
                if (refillBatches[currentBatchIndex].animations.Count >= maxConcurrentAnimations)
                {
                    currentBatchIndex++;
                }
            }
        }

        pendingRefill.Clear();

        // Execute refill animations
        if (refillBatches.Count > 0)
        {
            var refillTasks = new List<UniTask>();
            foreach (var batch in refillBatches)
            {
                refillTasks.Add(ExecuteAnimationBatch(batch));
            }
            await UniTask.WhenAll(refillTasks);
        }

        return (wasChanged, gemsActuallyCreated);
    }

    /// <summary>
    /// Handle emergency validation with batched animations
    /// </summary>
    private async UniTask<(bool wasChanged, int gemsCreated)> HandleEmergencyValidationWithBatching(BoundsInt bounds)
    {
        bool wasChanged = false;
        int gemsActuallyCreated = 0;

        var stillEmpty = validPositions.Where(p => !gems.ContainsKey(p) && !IsCellBlockedByObstacle(p)).ToList();

        if (stillEmpty.Count == 0)
        {
            return (wasChanged, gemsActuallyCreated);
        }

        var emergencyBatches = new List<AnimationBatch>();
        int currentBatchIndex = 0;

        foreach (var pos in stillEmpty)
        {
            if (!gems.ContainsKey(pos)) // Double-check
            {
                var newGem = CreateNewGem(pos, bounds.yMax + 10);
                gems[pos] = newGem;

                float distance = Mathf.Abs(pos.y - (bounds.yMax + 10));
                float duration = distance / fallSpeed;

                // Add to appropriate batch
                if (currentBatchIndex >= emergencyBatches.Count)
                {
                    emergencyBatches.Add(new AnimationBatch
                    {
                        animations = new List<GemAnimationData>(),
                        delay = currentBatchIndex * animationBatchDelay
                    });
                }

                emergencyBatches[currentBatchIndex].animations.Add(new GemAnimationData
                {
                    transform = newGem.transform,
                    targetPosition = new Vector3(pos.x + 0.5f, pos.y + 0.5f, 0),
                    duration = duration,
                    easeType = Ease.OutBounce
                });

                gemsActuallyCreated++;
                wasChanged = true;

                // Move to next batch if current one is full
                if (emergencyBatches[currentBatchIndex].animations.Count >= maxConcurrentAnimations)
                {
                    currentBatchIndex++;
                }
            }
        }

        // Execute emergency animations
        if (emergencyBatches.Count > 0)
        {
            var emergencyTasks = new List<UniTask>();
            foreach (var batch in emergencyBatches)
            {
                emergencyTasks.Add(ExecuteAnimationBatch(batch));
            }
            await UniTask.WhenAll(emergencyTasks);
        }

        return (wasChanged, gemsActuallyCreated);
    }

    /// <summary>
    /// Performance-optimized match detection with caching
    /// </summary>
    public List<MatchInfo> DetectAllMatchesOptimized(bool checkBonusShapes = true)
    {
        if (_matchFinder == null)
        {
            return DetectAllMatches(checkBonusShapes);
        }

        // Use cached match finder for better performance
        return _matchFinder.DetectMatchesInLayout(gems);
    }
}
