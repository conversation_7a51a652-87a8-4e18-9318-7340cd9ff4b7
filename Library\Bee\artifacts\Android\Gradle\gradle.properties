org.gradle.jvmargs=-Xmx4096M
org.gradle.parallel=true
unityStreamingAssets=.json
# Android Resolver Properties Start
android.useAndroidX=true
android.enableJetifier=true
# Android Resolver Properties End
unityTemplateVersion=20
unityProjectPath=F:/Match2D
unity.projectPath=F:/Match2D
unity.debugSymbolLevel=none
unity.buildToolsVersion=34.0.0
unity.minSdkVersion=23
unity.targetSdkVersion=36
unity.compileSdkVersion=36
unity.applicationId=com.PhantomTeam.PufflandAdventure
unity.abiFilters=arm64-v8a
unity.versionCode=13
unity.versionName=0.2.01
unity.namespace=com.PhantomTeam.PufflandAdventure
unity.agpVersion=8.7.2
unity.androidSdkPath=F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/SDK
unity.androidNdkPath=F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/NDK
unity.androidNdkVersion=27.2.12479018
unity.jdkPath=F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/OpenJDK
unity.javaCompatabilityVersion=VERSION_17
unity.installInBuildFolder=false
android.useAndroidX=true
android.enableJetifier=true
android.bundle.includeNativeDebugMetadata=false
org.gradle.welcome=never

android.useFullClasspathForDexingTransform = true