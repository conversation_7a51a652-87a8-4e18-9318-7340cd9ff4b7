{"Version": 1, "WorkspaceRootPath": "F:\\Match2D\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|f:\\match2d\\assets\\ownmatch3\\scripts\\main\\boardperformanceconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\main\\boardperformanceconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\monetization\\adsmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\monetization\\adsmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\main\\boardperformanceoptimizations.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\main\\boardperformanceoptimizations.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\main\\match3board.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\main\\match3board.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\utils\\cameramanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\utils\\cameramanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\devsdaddy\\gameshield\\demo\\scripts\\gameinstaller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\devsdaddy\\gameshield\\demo\\scripts\\gameinstaller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\security\\updategame.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\security\\updategame.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B97C4650-42C8-E7EF-6182-41119E3FA415}|Assembly-CSharp.csproj|F:\\Match2D\\assets\\samples\\ads mediation\\8.10.0\\unity levelplay sample\\scripts\\levelplaysample.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B97C4650-42C8-E7EF-6182-41119E3FA415}|Assembly-CSharp.csproj|solutionrelative:assets\\samples\\ads mediation\\8.10.0\\unity levelplay sample\\scripts\\levelplaysample.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\monetization\\adconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\monetization\\adconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\movetonextscene.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\movetonextscene.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\editor\\videoframeextractorwindow.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\editor\\videoframeextractorwindow.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\editor\\debugmigrationwindow.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\editor\\debugmigrationwindow.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\editor\\simplecameramanagereditor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\editor\\simplecameramanagereditor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\effects\\bonusgemeffects.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\effects\\bonusgemeffects.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\main\\bonusgemmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\main\\bonusgemmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\ui\\moveswarninganimation.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\ui\\moveswarninganimation.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\bonuses\\linerocketbonus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\bonuses\\linerocketbonus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\match3logic\\boardlogic.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\match3logic\\boardlogic.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C1B64A86-57FE-2331-A6B2-AFB0A1E75077}|EventFramework.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\devsdaddy\\shared\\eventframework\\eventmessenger.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C1B64A86-57FE-2331-A6B2-AFB0A1E75077}|EventFramework.csproj|solutionrelative:assets\\ownmatch3\\scripts\\devsdaddy\\shared\\eventframework\\eventmessenger.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Match2D\\Library\\PackageCache\\com.unity.ugui@ecde93d85e93\\Runtime\\TMP\\TMP_FontAsset.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Library\\PackageCache\\com.unity.ugui@ecde93d85e93\\Runtime\\TMP\\TMP_FontAsset.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\devsdaddy\\gameshield\\core\\gameshield.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\devsdaddy\\gameshield\\core\\gameshield.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\main\\gameprogressmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\main\\gameprogressmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\UnlockLevelsTest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Assets\\OwnMatch3\\Scripts\\UI\\UnlockLevelsTest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B97C4650-42C8-E7EF-6182-41119E3FA415}|Assembly-CSharp.csproj|F:\\Match2D\\assets\\gemhuntermatch\\scripts\\leveldata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B97C4650-42C8-E7EF-6182-41119E3FA415}|Assembly-CSharp.csproj|solutionrelative:assets\\gemhuntermatch\\scripts\\leveldata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\Users\\phant\\Desktop\\Data Recovery\\Match2D\\Assets\\OwnMatch3\\Scripts\\Examples\\HintSystemDemo.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\Users\\phant\\Desktop\\Data Recovery\\Match2D\\Assets\\OwnMatch3\\Scripts\\Editor\\LevelGeneratorWindow.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\EndGameIntegration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Assets\\OwnMatch3\\Scripts\\UI\\EndGameIntegration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\EndGameMenu.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Assets\\OwnMatch3\\Scripts\\UI\\EndGameMenu.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Editor\\AILevelGeneratorWindow.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Assets\\OwnMatch3\\Scripts\\Editor\\AILevelGeneratorWindow.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\Users\\phant\\Desktop\\Data Recovery\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\MovesWarningAnimation.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\Users\\phant\\Desktop\\Data Recovery\\Match2D\\Assets\\OwnMatch3\\Scripts\\Main\\Match3Board.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Editor\\UIBuilderRegistration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Assets\\OwnMatch3\\Scripts\\Editor\\UIBuilderRegistration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\CoreUI\\MenuUIController.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Assets\\OwnMatch3\\Scripts\\CoreUI\\MenuUIController.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Match2D\\Assets\\OwnMatch3\\DevsDaddy\\GameShield\\Core\\Modules\\Memory\\SecuredTypes\\SecuredString.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Assets\\OwnMatch3\\DevsDaddy\\GameShield\\Core\\Modules\\Memory\\SecuredTypes\\SecuredString.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Match2D\\Assets\\OwnMatch3\\DevsDaddy\\GameShield\\Core\\Modules\\Time\\TimeProtector.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Assets\\OwnMatch3\\DevsDaddy\\GameShield\\Core\\Modules\\Time\\TimeProtector.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Match2D\\Assets\\OwnMatch3\\DevsDaddy\\GameShield\\Demo\\Scripts\\GameInstaller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Assets\\OwnMatch3\\DevsDaddy\\GameShield\\Demo\\Scripts\\GameInstaller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\utils\\debugsettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\utils\\debugsettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\ui\\missionsuicontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\ui\\missionsuicontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\avatars\\avatarmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\avatars\\avatarmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\ui\\stageprogressbardebug.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\ui\\stageprogressbardebug.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\testing\\savesystemdebugger.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\testing\\savesystemdebugger.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\ui\\toolkit\\shoppanel.uss||{3B902123-F8A7-4915-9F01-361F908088D0}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\ui\\toolkit\\shoppanel.uss||{3B902123-F8A7-4915-9F01-361F908088D0}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\boosters\\boostermanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\boosters\\boostermanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\ui\\pregamepopupsetup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\ui\\pregamepopupsetup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\main\\audiomanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\main\\audiomanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\main\\gamestatemanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\main\\gamestatemanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\levels\\levelsetup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\levels\\levelsetup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\ai\\adaptivegenerationsystem.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\ai\\adaptivegenerationsystem.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\ui\\pregamepopupcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\ui\\pregamepopupcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\security\\gettimenetinitializer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\security\\gettimenetinitializer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\EmailManager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:Assets\\OwnMatch3\\Scripts\\UI\\EmailManager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Testing\\CurrencyTestController.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:Assets\\OwnMatch3\\Scripts\\Testing\\CurrencyTestController.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\CurrencyAttractorUI.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:Assets\\OwnMatch3\\Scripts\\UI\\CurrencyAttractorUI.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\StageProgressBar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:Assets\\OwnMatch3\\Scripts\\UI\\StageProgressBar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\ui\\levels ui\\stageprogressbar.uss||{3B902123-F8A7-4915-9F01-361F908088D0}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\ui\\levels ui\\stageprogressbar.uss||{3B902123-F8A7-4915-9F01-361F908088D0}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\ui\\levels ui\\scrollfixes.uss||{3B902123-F8A7-4915-9F01-361F908088D0}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\ui\\levels ui\\scrollfixes.uss||{3B902123-F8A7-4915-9F01-361F908088D0}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\utils\\debugmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\utils\\debugmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\security\\gettimenet.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\security\\gettimenet.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\MenuUIController.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:Assets\\OwnMatch3\\Scripts\\UI\\MenuUIController.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\ui\\toolkit\\dailyrewards.uss||{3B902123-F8A7-4915-9F01-361F908088D0}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\ui\\toolkit\\dailyrewards.uss||{3B902123-F8A7-4915-9F01-361F908088D0}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\DailyRewardsManager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:Assets\\OwnMatch3\\Scripts\\UI\\DailyRewardsManager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\ui\\particles\\simpleparticlecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\ui\\particles\\simpleparticlecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\ui\\particles\\particleelement.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\ui\\particles\\particleelement.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Editor\\EncryptionKeyGeneratorWindow.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:Assets\\OwnMatch3\\Scripts\\Editor\\EncryptionKeyGeneratorWindow.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Editor\\GameProgressManagerEditor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:Assets\\OwnMatch3\\Scripts\\Editor\\GameProgressManagerEditor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Editor\\ModernAILevelGeneratorWindow.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:Assets\\OwnMatch3\\Scripts\\Editor\\ModernAILevelGeneratorWindow.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Editor\\EditShapeMatch.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:Assets\\OwnMatch3\\Scripts\\Editor\\EditShapeMatch.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\testing\\testingsecuredtypes.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\testing\\testingsecuredtypes.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\utils\\objectpoolermonitor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\utils\\objectpoolermonitor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\EndGamePopupController.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:Assets\\OwnMatch3\\Scripts\\UI\\EndGamePopupController.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\examples\\bonusgemsystemexample.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\examples\\bonusgemsystemexample.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\ui\\scoreprogressbarcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\ui\\scoreprogressbarcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\main\\scoremanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\main\\scoremanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\ui\\custom ui elements\\curvedprogressbar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\ui\\custom ui elements\\curvedprogressbar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\ui\\custom ui elements\\starratingelement.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\ui\\custom ui elements\\starratingelement.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\ui\\goaldisplayconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\ui\\goaldisplayconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\bonuses\\bonusactivitytracker.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\bonuses\\bonusactivitytracker.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\utils\\camerasetuphelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\utils\\camerasetuphelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\ai\\ailevelgenerator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\ai\\ailevelgenerator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\ai\\adaptivecomponents.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\ai\\adaptivecomponents.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\bonuses\\gemdestructioneffect.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\bonuses\\gemdestructioneffect.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\bonuses\\rocketexplosioneffect.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\bonuses\\rocketexplosioneffect.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\bonuses\\linerendererextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\bonuses\\linerendererextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\bonuses\\rockettraileffect.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\bonuses\\rockettraileffect.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\main\\levelcompletionhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\main\\levelcompletionhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\GemAttractorUI.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:Assets\\OwnMatch3\\Scripts\\UI\\GemAttractorUI.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\GameUIDocument.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:Assets\\OwnMatch3\\Scripts\\UI\\GameUIDocument.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\main\\gemsoundsystem.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\main\\gemsoundsystem.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\bonuses\\bonus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\bonuses\\bonus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\match3match\\boardmatchfinderjobs.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\match3match\\boardmatchfinderjobs.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\match3match\\shapematcher.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\match3match\\shapematcher.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\match3logic\\boardshufflejobs.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\match3logic\\boardshufflejobs.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\levels\\levelloaderjobs.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\levels\\levelloaderjobs.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\utils\\fpscounter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\utils\\fpscounter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\UIManager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:Assets\\OwnMatch3\\Scripts\\UI\\UIManager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\ComboAnnouncer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:Assets\\OwnMatch3\\Scripts\\UI\\ComboAnnouncer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\ComboManager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:Assets\\OwnMatch3\\Scripts\\UI\\ComboManager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\ui\\toolkit\\gameui.uss||{3B902123-F8A7-4915-9F01-361F908088D0}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\ui\\toolkit\\gameui.uss||{3B902123-F8A7-4915-9F01-361F908088D0}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\match3setup\\boardjsoninit.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\match3setup\\boardjsoninit.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\level\\leveldata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\level\\leveldata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\match3setup\\framebuilder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\match3setup\\framebuilder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\levels\\levelloader.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\levels\\levelloader.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\PointsAnnouncer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:Assets\\OwnMatch3\\Scripts\\UI\\PointsAnnouncer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\GameUI.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:Assets\\OwnMatch3\\Scripts\\UI\\GameUI.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\goals\\goal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\goals\\goal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\bonuses\\bombxbonus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\bonuses\\bombxbonus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\bonuses\\bombbonus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\bonuses\\bombbonus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\match3setup\\obstaclesoundsystemsetup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\match3setup\\obstaclesoundsystemsetup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\match3setup\\obstaclesoundsystem.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\match3setup\\obstaclesoundsystem.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\match3input\\boardinput.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\match3input\\boardinput.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\utils\\logtofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\utils\\logtofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\utils\\objectpooler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\utils\\objectpooler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\gems\\gemnew.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\gems\\gemnew.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\match3obstacles\\obstacletiledata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\match3obstacles\\obstacletiledata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\bonuses\\fishbonus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\bonuses\\fishbonus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\bonuses\\colorbonus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\bonuses\\colorbonus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\match3obstacles\\obstaclehandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\match3obstacles\\obstaclehandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\match3setup\\boardutils.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\match3setup\\boardutils.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\match3match\\boardmatchresolver.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\match3match\\boardmatchresolver.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\match3match\\boardmatchfinder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\match3match\\boardmatchfinder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\obstaclenew.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\obstaclenew.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|F:\\Match2D\\assets\\ownmatch3\\scripts\\matchinfo.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A1ED07-2DA9-E77E-E029-E4047CBFA848}|OwnMatch3.csproj|solutionrelative:assets\\ownmatch3\\scripts\\matchinfo.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Utils\\PerformanceCache.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Assets\\OwnMatch3\\Scripts\\Utils\\PerformanceCache.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Utils\\PerformanceCacheSetup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Assets\\OwnMatch3\\Scripts\\Utils\\PerformanceCacheSetup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Document", "DocumentIndex": 1, "Title": "AdsManager.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Monetization\\AdsManager.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Monetization\\AdsManager.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Monetization\\AdsManager.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Monetization\\AdsManager.cs", "ViewState": "AgIAACwAAAAAAAAAAAAUwFAAAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T23:25:16.13Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "BoardPerformanceConfig.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Main\\BoardPerformanceConfig.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Main\\BoardPerformanceConfig.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Main\\BoardPerformanceConfig.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Main\\BoardPerformanceConfig.cs", "ViewState": "AgIAAKEAAAAAAAAAAAAEwLMAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-26T11:20:59.713Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:1:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "BoardPerformanceOptimizations.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Main\\BoardPerformanceOptimizations.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Main\\BoardPerformanceOptimizations.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Main\\BoardPerformanceOptimizations.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Main\\BoardPerformanceOptimizations.cs", "ViewState": "AgIAAHgAAAAAAAAAAAAAAEIAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-26T11:19:29.346Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "GameInstaller.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\DevsDaddy\\GameShield\\Demo\\Scripts\\GameInstaller.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\DevsDaddy\\GameShield\\Demo\\Scripts\\GameInstaller.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\DevsDaddy\\GameShield\\Demo\\Scripts\\GameInstaller.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\DevsDaddy\\GameShield\\Demo\\Scripts\\GameInstaller.cs", "ViewState": "AgIAACQAAAAAAAAAAAAowDsAAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T21:23:32.437Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "LevelPlaySample.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\Samples\\Ads Mediation\\8.10.0\\Unity LevelPlay Sample\\Scripts\\LevelPlaySample.cs", "RelativeDocumentMoniker": "Assets\\Samples\\Ads Mediation\\8.10.0\\Unity LevelPlay Sample\\Scripts\\LevelPlaySample.cs", "ToolTip": "F:\\Match2D\\Assets\\Samples\\Ads Mediation\\8.10.0\\Unity LevelPlay Sample\\Scripts\\LevelPlaySample.cs", "RelativeToolTip": "Assets\\Samples\\Ads Mediation\\8.10.0\\Unity LevelPlay Sample\\Scripts\\LevelPlaySample.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAQwBEAAAAHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T13:36:47.824Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "MoveToNextScene.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\MoveToNextScene.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\MoveToNextScene.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\MoveToNextScene.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\MoveToNextScene.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAABBAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T22:39:29.306Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "VideoFrameExtractorWindow.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Editor\\VideoFrameExtractorWindow.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Editor\\VideoFrameExtractorWindow.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Editor\\VideoFrameExtractorWindow.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Editor\\VideoFrameExtractorWindow.cs", "ViewState": "AgIAAIkAAAAAAAAAAAAAAJAAAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T21:59:33.258Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "DebugMigrationWindow.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Editor\\DebugMigrationWindow.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Editor\\DebugMigrationWindow.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Editor\\DebugMigrationWindow.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Editor\\DebugMigrationWindow.cs", "ViewState": "AgIAAGoEAAAAAAAAAAAAAHMEAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T21:58:02.776Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "SimpleCameraManagerEditor.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Editor\\SimpleCameraManagerEditor.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Editor\\SimpleCameraManagerEditor.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Editor\\SimpleCameraManagerEditor.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Editor\\SimpleCameraManagerEditor.cs", "ViewState": "AgIAAO0AAAAAAAAAAAAAAPsAAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T21:56:46.125Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "BonusGemEffects.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Effects\\BonusGemEffects.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Effects\\BonusGemEffects.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Effects\\BonusGemEffects.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Effects\\BonusGemEffects.cs", "ViewState": "AgIAAHcBAAAAAAAAAAAIwIkBAABuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T21:31:07.631Z"}, {"$type": "Document", "DocumentIndex": 122, "Title": "PerformanceCache.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Utils\\PerformanceCache.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Utils\\PerformanceCache.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Utils\\PerformanceCache.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Utils\\PerformanceCache.cs", "ViewState": "AgIAABcAAAAAAAAAAAAowB8AAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T15:01:33.61Z"}, {"$type": "Document", "DocumentIndex": 123, "Title": "PerformanceCacheSetup.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Utils\\PerformanceCacheSetup.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Utils\\PerformanceCacheSetup.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Utils\\PerformanceCacheSetup.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Utils\\PerformanceCacheSetup.cs", "ViewState": "AgIAABcAAAAAAAAAAAAQwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T15:01:25.754Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "EventMessenger.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\DevsDaddy\\Shared\\EventFramework\\EventMessenger.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\DevsDaddy\\Shared\\EventFramework\\EventMessenger.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\DevsDaddy\\Shared\\EventFramework\\EventMessenger.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\DevsDaddy\\Shared\\EventFramework\\EventMessenger.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAAACIAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T14:05:37.314Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "TMP_FontAsset.cs", "DocumentMoniker": "F:\\Match2D\\Library\\PackageCache\\com.unity.ugui@ecde93d85e93\\Runtime\\TMP\\TMP_FontAsset.cs", "RelativeDocumentMoniker": "Library\\PackageCache\\com.unity.ugui@ecde93d85e93\\Runtime\\TMP\\TMP_FontAsset.cs", "ToolTip": "F:\\Match2D\\Library\\PackageCache\\com.unity.ugui@ecde93d85e93\\Runtime\\TMP\\TMP_FontAsset.cs", "RelativeToolTip": "Library\\PackageCache\\com.unity.ugui@ecde93d85e93\\Runtime\\TMP\\TMP_FontAsset.cs", "ViewState": "AgIAAB4DAAAAAAAAAAAqwDIDAACYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T14:05:26.385Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "GameShield.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\DevsDaddy\\GameShield\\Core\\GameShield.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\DevsDaddy\\GameShield\\Core\\GameShield.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\DevsDaddy\\GameShield\\Core\\GameShield.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\DevsDaddy\\GameShield\\Core\\GameShield.cs", "ViewState": "AgIAAD4BAAAAAAAAAAAAAFkBAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T13:29:59.686Z"}, {"$type": "Document", "DocumentIndex": 36, "Title": "DebugSettings.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Utils\\DebugSettings.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Utils\\DebugSettings.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Utils\\DebugSettings.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Utils\\DebugSettings.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAL8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T13:22:55.167Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "AdConfig.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Monetization\\AdConfig.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Monetization\\AdConfig.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Monetization\\AdConfig.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Monetization\\AdConfig.cs", "ViewState": "AgIAADAAAAAAAAAAAAAkwBEAAAA9AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T13:00:25.388Z"}, {"$type": "Document", "DocumentIndex": 37, "Title": "MissionsUIController.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\MissionsUIController.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\UI\\MissionsUIController.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\MissionsUIController.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\UI\\MissionsUIController.cs", "ViewState": "AgIAAJoCAAAAAAAAAAAqwLACAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T12:31:04.307Z"}, {"$type": "Document", "DocumentIndex": 38, "Title": "AvatarManager.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Avatars\\AvatarManager.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Avatars\\AvatarManager.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Avatars\\AvatarManager.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Avatars\\AvatarManager.cs", "ViewState": "AgIAADwAAAAAAAAAAADwv0EAAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T12:02:23.145Z"}, {"$type": "Document", "DocumentIndex": 39, "Title": "StageProgressBarDebug.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\StageProgressBarDebug.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\UI\\StageProgressBarDebug.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\StageProgressBarDebug.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\UI\\StageProgressBarDebug.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T11:39:39.285Z"}, {"$type": "Document", "DocumentIndex": 40, "Title": "SaveSystemDebugger.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Testing\\SaveSystemDebugger.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Testing\\SaveSystemDebugger.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Testing\\SaveSystemDebugger.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Testing\\SaveSystemDebugger.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T11:39:08.148Z"}, {"$type": "Document", "DocumentIndex": 41, "Title": "ShopPanel.uss", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\UI\\Toolkit\\ShopPanel.uss", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\UI\\Toolkit\\ShopPanel.uss", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\UI\\Toolkit\\ShopPanel.uss", "RelativeToolTip": "Assets\\OwnMatch3\\UI\\Toolkit\\ShopPanel.uss", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003000|", "WhenOpened": "2025-07-22T20:47:17.216Z"}, {"$type": "Document", "DocumentIndex": 42, "Title": "BoosterManager.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Boosters\\BoosterManager.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Boosters\\BoosterManager.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Boosters\\BoosterManager.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Boosters\\BoosterManager.cs", "ViewState": "AgIAALwAAAAAAAAAAAA1wMkAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T20:44:53.325Z"}, {"$type": "Document", "DocumentIndex": 43, "Title": "PreGamePopupSetup.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\PreGamePopupSetup.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\UI\\PreGamePopupSetup.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\PreGamePopupSetup.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\UI\\PreGamePopupSetup.cs", "ViewState": "AgIAABgAAAAAAAAAAAAowBgAAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T00:12:07.964Z"}, {"$type": "Document", "DocumentIndex": 44, "Title": "AudioManager.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Main\\AudioManager.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Main\\AudioManager.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Main\\AudioManager.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Main\\AudioManager.cs", "ViewState": "AgIAAEUBAAAAAAAAAAAuwGwBAAB4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T00:02:24.823Z"}, {"$type": "Document", "DocumentIndex": 45, "Title": "GameStateManager.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Main\\GameStateManager.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Main\\GameStateManager.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Main\\GameStateManager.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Main\\GameStateManager.cs", "ViewState": "AgIAANwAAAAAAAAAAAAuwPMAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T23:54:09.451Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "MenuUIController.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\CoreUI\\MenuUIController.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\CoreUI\\MenuUIController.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\CoreUI\\MenuUIController.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\CoreUI\\MenuUIController.cs", "ViewState": "AgIAAEUBAAAAAAAAAAAQwG0BAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T22:59:28.802Z"}, {"$type": "Document", "DocumentIndex": 46, "Title": "LevelSetup.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Levels\\LevelSetup.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Levels\\LevelSetup.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Levels\\LevelSetup.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Levels\\LevelSetup.cs", "ViewState": "AgIAAFUAAAAAAAAAAAAAAHwAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T22:51:37.18Z"}, {"$type": "Document", "DocumentIndex": 47, "Title": "AdaptiveGenerationSystem.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\AI\\AdaptiveGenerationSystem.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\AI\\AdaptiveGenerationSystem.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\AI\\AdaptiveGenerationSystem.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\AI\\AdaptiveGenerationSystem.cs", "ViewState": "AgIAADwAAAAAAAAAAAAqwEcAAABHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T22:17:35.857Z"}, {"$type": "Document", "DocumentIndex": 48, "Title": "PreGamePopupController.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\PreGamePopupController.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\UI\\PreGamePopupController.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\PreGamePopupController.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\UI\\PreGamePopupController.cs", "ViewState": "AgIAACQAAAAAAAAAAAAwwL0AAABFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T19:33:38.833Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "SecuredString.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\DevsDaddy\\GameShield\\Core\\Modules\\Memory\\SecuredTypes\\SecuredString.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\DevsDaddy\\GameShield\\Core\\Modules\\Memory\\SecuredTypes\\SecuredString.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\DevsDaddy\\GameShield\\Core\\Modules\\Memory\\SecuredTypes\\SecuredString.cs", "RelativeToolTip": "Assets\\OwnMatch3\\DevsDaddy\\GameShield\\Core\\Modules\\Memory\\SecuredTypes\\SecuredString.cs", "ViewState": "AgIAABUAAAAAAAAAAAAAwCkAAABFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T16:38:16.519Z"}, {"$type": "Document", "DocumentIndex": 49, "Title": "GetTimeNetInitializer.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Security\\GetTimeNetInitializer.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Security\\GetTimeNetInitializer.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Security\\GetTimeNetInitializer.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Security\\GetTimeNetInitializer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T16:35:55.946Z"}, {"$type": "Document", "DocumentIndex": 50, "Title": "EmailManager.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\EmailManager.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\UI\\EmailManager.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\EmailManager.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\UI\\EmailManager.cs", "ViewState": "AgIAABgBAAAAAAAAAAAowNwAAABPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T16:08:55.014Z"}, {"$type": "Document", "DocumentIndex": 51, "Title": "CurrencyTestController.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Testing\\CurrencyTestController.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Testing\\CurrencyTestController.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Testing\\CurrencyTestController.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Testing\\CurrencyTestController.cs", "ViewState": "AgIAAMgAAAAAAAAAAAAYwMgAAAA3AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T15:09:53.717Z"}, {"$type": "Document", "DocumentIndex": 52, "Title": "CurrencyAttractorUI.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\CurrencyAttractorUI.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\UI\\CurrencyAttractorUI.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\CurrencyAttractorUI.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\UI\\CurrencyAttractorUI.cs", "ViewState": "AgIAANoAAAAAAAAAAAAAwOwAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T15:02:12.785Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "UnlockLevelsTest.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\UnlockLevelsTest.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\UI\\UnlockLevelsTest.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\UnlockLevelsTest.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\UI\\UnlockLevelsTest.cs", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-19T14:10:23.928Z"}, {"$type": "Document", "DocumentIndex": 53, "Title": "StageProgressBar.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\StageProgressBar.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\UI\\StageProgressBar.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\StageProgressBar.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\UI\\StageProgressBar.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAAAAcBAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-19T13:02:24.201Z"}, {"$type": "Document", "DocumentIndex": 54, "Title": "StageProgressBar.uss", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\UI\\Levels UI\\StageProgressBar.uss", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\UI\\Levels UI\\StageProgressBar.uss", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\UI\\Levels UI\\StageProgressBar.uss", "RelativeToolTip": "Assets\\OwnMatch3\\UI\\Levels UI\\StageProgressBar.uss", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003000|", "WhenOpened": "2025-07-19T13:01:36.247Z"}, {"$type": "Document", "DocumentIndex": 55, "Title": "ScrollFixes.uss", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\UI\\Levels UI\\ScrollFixes.uss", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\UI\\Levels UI\\ScrollFixes.uss", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\UI\\Levels UI\\ScrollFixes.uss", "RelativeToolTip": "Assets\\OwnMatch3\\UI\\Levels UI\\ScrollFixes.uss", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003000|", "WhenOpened": "2025-07-19T10:04:25.161Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "UpdateGame.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Security\\UpdateGame.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Security\\UpdateGame.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Security\\UpdateGame.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Security\\UpdateGame.cs", "ViewState": "AgIAAB0AAAAAAAAAAAAQwCgAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T15:58:29.483Z"}, {"$type": "Document", "DocumentIndex": 34, "Title": "TimeProtector.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\DevsDaddy\\GameShield\\Core\\Modules\\Time\\TimeProtector.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\DevsDaddy\\GameShield\\Core\\Modules\\Time\\TimeProtector.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\DevsDaddy\\GameShield\\Core\\Modules\\Time\\TimeProtector.cs", "RelativeToolTip": "Assets\\OwnMatch3\\DevsDaddy\\GameShield\\Core\\Modules\\Time\\TimeProtector.cs", "ViewState": "AgIAADcBAAAAAAAAAAAowFEBAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T22:59:57.715Z"}, {"$type": "Document", "DocumentIndex": 56, "Title": "DebugManager.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Utils\\DebugManager.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Utils\\DebugManager.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Utils\\DebugManager.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Utils\\DebugManager.cs", "ViewState": "AgIAAPkAAAAAAAAAAAAQwB4BAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T22:28:06.668Z"}, {"$type": "Document", "DocumentIndex": 57, "Title": "GetTimeNet.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Security\\GetTimeNet.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Security\\GetTimeNet.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Security\\GetTimeNet.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Security\\GetTimeNet.cs", "ViewState": "AgIAAEAAAAAAAAAAAAAvwFkAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T22:04:08.209Z"}, {"$type": "Document", "DocumentIndex": 35, "Title": "GameInstaller.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\DevsDaddy\\GameShield\\Demo\\Scripts\\GameInstaller.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\DevsDaddy\\GameShield\\Demo\\Scripts\\GameInstaller.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\DevsDaddy\\GameShield\\Demo\\Scripts\\GameInstaller.cs", "RelativeToolTip": "Assets\\OwnMatch3\\DevsDaddy\\GameShield\\Demo\\Scripts\\GameInstaller.cs", "ViewState": "AgIAAC8AAAAAAAAAAAAAABYAAABRAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T21:19:09.248Z"}, {"$type": "Document", "DocumentIndex": 58, "Title": "MenuUIController.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\MenuUIController.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\UI\\MenuUIController.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\MenuUIController.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\UI\\MenuUIController.cs", "ViewState": "AgIAAKQBAAAAAAAAAAAAALEBAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T16:19:07.604Z"}, {"$type": "Document", "DocumentIndex": 59, "Title": "DailyRewards.uss", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\UI\\Toolkit\\DailyRewards.uss", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\UI\\Toolkit\\DailyRewards.uss", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\UI\\Toolkit\\DailyRewards.uss", "RelativeToolTip": "Assets\\OwnMatch3\\UI\\Toolkit\\DailyRewards.uss", "ViewState": "AgIAAEcAAAAAAAAAAAAAAEcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003000|", "WhenOpened": "2025-07-17T15:59:01.269Z"}, {"$type": "Document", "DocumentIndex": 60, "Title": "DailyRewardsManager.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\DailyRewardsManager.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\UI\\DailyRewardsManager.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\DailyRewardsManager.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\UI\\DailyRewardsManager.cs", "ViewState": "AgIAAEIAAAAAAAAAAAAmwFUAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T15:55:26.063Z"}, {"$type": "Document", "DocumentIndex": 61, "Title": "SimpleParticleController.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\Particles\\SimpleParticleController.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\UI\\Particles\\SimpleParticleController.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\Particles\\SimpleParticleController.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\UI\\Particles\\SimpleParticleController.cs", "ViewState": "AgIAAD4AAAAAAAAAAAAIwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T23:04:29.645Z"}, {"$type": "Document", "DocumentIndex": 62, "Title": "ParticleElement.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\Particles\\ParticleElement.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\UI\\Particles\\ParticleElement.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\Particles\\ParticleElement.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\UI\\Particles\\ParticleElement.cs", "ViewState": "AgIAAFgBAAAAAAAAAAAiwGkBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T19:00:52.285Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "UIBuilderRegistration.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Editor\\UIBuilderRegistration.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Editor\\UIBuilderRegistration.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Editor\\UIBuilderRegistration.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Editor\\UIBuilderRegistration.cs", "ViewState": "AgIAABYAAAAAAAAAAAAswCkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T17:59:44.539Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "CameraManager.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Utils\\CameraManager.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Utils\\CameraManager.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Utils\\CameraManager.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Utils\\CameraManager.cs", "ViewState": "AgIAAA8BAAAAAAAAAIBJwBwBAABIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T22:32:34.351Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "Match3Board.cs", "DocumentMoniker": "D:\\Users\\phant\\Desktop\\Data Recovery\\Match2D\\Assets\\OwnMatch3\\Scripts\\Main\\Match3Board.cs", "ToolTip": "D:\\Users\\phant\\Desktop\\Data Recovery\\Match2D\\Assets\\OwnMatch3\\Scripts\\Main\\Match3Board.cs", "ViewState": "AgIAAEUAAAAAAAAAAAAswHAAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T00:21:59.693Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 29, "Title": "MovesWarningAnimation.cs", "DocumentMoniker": "D:\\Users\\phant\\Desktop\\Data Recovery\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\MovesWarningAnimation.cs", "ToolTip": "D:\\Users\\phant\\Desktop\\Data Recovery\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\MovesWarningAnimation.cs", "ViewState": "AgIAADwBAAAAAAAAAAAvwFEBAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T00:21:11.244Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 63, "Title": "EncryptionKeyGeneratorWindow.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Editor\\EncryptionKeyGeneratorWindow.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Editor\\EncryptionKeyGeneratorWindow.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Editor\\EncryptionKeyGeneratorWindow.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Editor\\EncryptionKeyGeneratorWindow.cs", "ViewState": "AgIAANAAAAAAAAAAAAAqwNsAAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T00:54:50.904Z"}, {"$type": "Document", "DocumentIndex": 64, "Title": "GameProgressManagerEditor.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Editor\\GameProgressManagerEditor.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Editor\\GameProgressManagerEditor.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Editor\\GameProgressManagerEditor.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Editor\\GameProgressManagerEditor.cs", "ViewState": "AgIAAAkDAAAAAAAAAAAAACEDAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-13T20:26:30.552Z"}, {"$type": "Document", "DocumentIndex": 65, "Title": "ModernAILevelGeneratorWindow.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Editor\\ModernAILevelGeneratorWindow.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Editor\\ModernAILevelGeneratorWindow.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Editor\\ModernAILevelGeneratorWindow.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Editor\\ModernAILevelGeneratorWindow.cs", "ViewState": "AgIAAJwAAAAAAAAAAAASwLAAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-13T16:39:00.179Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "AILevelGeneratorWindow.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Editor\\AILevelGeneratorWindow.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Editor\\AILevelGeneratorWindow.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Editor\\AILevelGeneratorWindow.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Editor\\AILevelGeneratorWindow.cs", "ViewState": "AgIAADIGAAAAAAAAAAAAADUGAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T11:40:53.351Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "EndGameIntegration.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\EndGameIntegration.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\UI\\EndGameIntegration.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\EndGameIntegration.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\UI\\EndGameIntegration.cs", "ViewState": "AgIAACcAAAAAAAAAAAAowAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T11:06:52.833Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "EndGameMenu.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\EndGameMenu.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\UI\\EndGameMenu.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\EndGameMenu.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\UI\\EndGameMenu.cs", "ViewState": "AgIAACcBAAAAAAAAAAAuwD8BAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T11:05:44.424Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "LevelGeneratorWindow.cs", "DocumentMoniker": "D:\\Users\\phant\\Desktop\\Data Recovery\\Match2D\\Assets\\OwnMatch3\\Scripts\\Editor\\LevelGeneratorWindow.cs", "ToolTip": "D:\\Users\\phant\\Desktop\\Data Recovery\\Match2D\\Assets\\OwnMatch3\\Scripts\\Editor\\LevelGeneratorWindow.cs", "ViewState": "AgIAADACAAAAAAAAAAAAAEACAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T16:22:14.289Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "HintSystemDemo.cs", "DocumentMoniker": "D:\\Users\\phant\\Desktop\\Data Recovery\\Match2D\\Assets\\OwnMatch3\\Scripts\\Examples\\HintSystemDemo.cs", "ToolTip": "D:\\Users\\phant\\Desktop\\Data Recovery\\Match2D\\Assets\\OwnMatch3\\Scripts\\Examples\\HintSystemDemo.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACkAAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T23:04:14.549Z"}, {"$type": "Document", "DocumentIndex": 66, "Title": "EditShapeMatch.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Editor\\EditShapeMatch.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Editor\\EditShapeMatch.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Editor\\EditShapeMatch.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Editor\\EditShapeMatch.cs", "ViewState": "AgIAADoGAAAAAAAAAAAqwE4GAABQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T21:46:12.112Z"}, {"$type": "Document", "DocumentIndex": 67, "Title": "TestingSecuredTypes.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Testing\\TestingSecuredTypes.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Testing\\TestingSecuredTypes.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Testing\\TestingSecuredTypes.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Testing\\TestingSecuredTypes.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABgAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T23:32:39.034Z"}, {"$type": "Document", "DocumentIndex": 68, "Title": "ObjectPoolerMonitor.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Utils\\ObjectPoolerMonitor.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Utils\\ObjectPoolerMonitor.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Utils\\ObjectPoolerMonitor.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Utils\\ObjectPoolerMonitor.cs", "ViewState": "AgIAABcBAAAAAAAAAAAAAEEAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T13:59:07.781Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "GameProgressManager.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Main\\GameProgressManager.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Main\\GameProgressManager.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Main\\GameProgressManager.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Main\\GameProgressManager.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-13T12:57:10.858Z"}, {"$type": "Document", "DocumentIndex": 69, "Title": "EndGamePopupController.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\EndGamePopupController.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\UI\\EndGamePopupController.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\EndGamePopupController.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\UI\\EndGamePopupController.cs", "ViewState": "AgIAAPoCAAAAAAAAAAAQwAsDAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T21:50:53.413Z"}, {"$type": "Document", "DocumentIndex": 70, "Title": "BonusGemSystemExample.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Examples\\BonusGemSystemExample.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Examples\\BonusGemSystemExample.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Examples\\BonusGemSystemExample.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Examples\\BonusGemSystemExample.cs", "ViewState": "AgIAABoAAAAAAAAAAAAswDAAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T14:35:43.29Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "BonusGemManager.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Main\\BonusGemManager.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Main\\BonusGemManager.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Main\\BonusGemManager.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Main\\BonusGemManager.cs", "ViewState": "AgIAAM0AAAAAAAAAAAAcwOAAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T14:02:48.548Z"}, {"$type": "Document", "DocumentIndex": 71, "Title": "ScoreProgressBarController.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\ScoreProgressBarController.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\UI\\ScoreProgressBarController.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\ScoreProgressBarController.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\UI\\ScoreProgressBarController.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAxwBkAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T13:15:23.901Z"}, {"$type": "Document", "DocumentIndex": 72, "Title": "ScoreManager.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Main\\ScoreManager.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Main\\ScoreManager.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Main\\ScoreManager.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Main\\ScoreManager.cs", "ViewState": "AgIAAPAAAAAAAAAAAAAAAA8BAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T13:09:39.133Z"}, {"$type": "Document", "DocumentIndex": 73, "Title": "CurvedProgressBar.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\UI\\Custom UI Elements\\CurvedProgressBar.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\UI\\Custom UI Elements\\CurvedProgressBar.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\UI\\Custom UI Elements\\CurvedProgressBar.cs", "RelativeToolTip": "Assets\\OwnMatch3\\UI\\Custom UI Elements\\CurvedProgressBar.cs", "ViewState": "AgIAAH4CAAAAAAAAAAAAAJACAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T18:11:02.168Z"}, {"$type": "Document", "DocumentIndex": 74, "Title": "StarRatingElement.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\UI\\Custom UI Elements\\StarRatingElement.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\UI\\Custom UI Elements\\StarRatingElement.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\UI\\Custom UI Elements\\StarRatingElement.cs", "RelativeToolTip": "Assets\\OwnMatch3\\UI\\Custom UI Elements\\StarRatingElement.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T17:02:27.292Z"}, {"$type": "Document", "DocumentIndex": 75, "Title": "GoalDisplayConfiguration.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\GoalDisplayConfiguration.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\UI\\GoalDisplayConfiguration.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\GoalDisplayConfiguration.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\UI\\GoalDisplayConfiguration.cs", "ViewState": "AgIAAC8AAAAAAAAAAAAAAEMAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T15:16:54.491Z"}, {"$type": "Document", "DocumentIndex": 76, "Title": "BonusActivityTracker.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Bonuses\\BonusActivityTracker.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Bonuses\\BonusActivityTracker.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Bonuses\\BonusActivityTracker.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Bonuses\\BonusActivityTracker.cs", "ViewState": "AgIAAJMAAAAAAAAAAAAmwJwAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T00:28:42.774Z"}, {"$type": "Document", "DocumentIndex": 77, "Title": "CameraSetupHelper.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Utils\\CameraSetupHelper.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Utils\\CameraSetupHelper.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Utils\\CameraSetupHelper.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Utils\\CameraSetupHelper.cs", "ViewState": "AgIAAFAAAAAAAAAAAAAwwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T13:47:22.112Z"}, {"$type": "Document", "DocumentIndex": 78, "Title": "AILevelGenerator.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\AI\\AILevelGenerator.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\AI\\AILevelGenerator.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\AI\\AILevelGenerator.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\AI\\AILevelGenerator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAhwAwKAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T11:34:18.583Z"}, {"$type": "Document", "DocumentIndex": 79, "Title": "AdaptiveComponents.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\AI\\AdaptiveComponents.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\AI\\AdaptiveComponents.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\AI\\AdaptiveComponents.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\AI\\AdaptiveComponents.cs", "ViewState": "AgIAADEAAAAAAAAAAAAgwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T11:17:53.936Z"}, {"$type": "Document", "DocumentIndex": 80, "Title": "GemDestructionEffect.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Bonuses\\GemDestructionEffect.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Bonuses\\GemDestructionEffect.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Bonuses\\GemDestructionEffect.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Bonuses\\GemDestructionEffect.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAA0AAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T21:11:20.37Z"}, {"$type": "Document", "DocumentIndex": 81, "Title": "RocketExplosionEffect.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Bonuses\\RocketExplosionEffect.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Bonuses\\RocketExplosionEffect.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Bonuses\\RocketExplosionEffect.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Bonuses\\RocketExplosionEffect.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAEAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T20:57:48.6Z"}, {"$type": "Document", "DocumentIndex": 82, "Title": "LineRendererExtensions.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Bonuses\\LineRendererExtensions.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Bonuses\\LineRendererExtensions.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Bonuses\\LineRendererExtensions.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Bonuses\\LineRendererExtensions.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T20:57:12.162Z"}, {"$type": "Document", "DocumentIndex": 83, "Title": "RocketTrailEffect.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Bonuses\\RocketTrailEffect.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Bonuses\\RocketTrailEffect.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Bonuses\\RocketTrailEffect.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Bonuses\\RocketTrailEffect.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABgAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T20:56:52.525Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "MovesWarningAnimation.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\MovesWarningAnimation.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\UI\\MovesWarningAnimation.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\MovesWarningAnimation.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\UI\\MovesWarningAnimation.cs", "ViewState": "AgIAAHUAAAAAAAAAAAAIwHsAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T23:04:15.876Z"}, {"$type": "Document", "DocumentIndex": 84, "Title": "LevelCompletionHandler.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Main\\LevelCompletionHandler.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Main\\LevelCompletionHandler.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Main\\LevelCompletionHandler.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Main\\LevelCompletionHandler.cs", "ViewState": "AgIAALwAAAAAAAAAAAAkwCYAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T22:16:11.947Z"}, {"$type": "Document", "DocumentIndex": 85, "Title": "GemAttractorUI.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\GemAttractorUI.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\UI\\GemAttractorUI.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\GemAttractorUI.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\UI\\GemAttractorUI.cs", "ViewState": "AgIAAOAAAAAAAAAAAAAvwPMAAABYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T15:40:55.655Z"}, {"$type": "Document", "DocumentIndex": 86, "Title": "GameUIDocument.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\GameUIDocument.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\UI\\GameUIDocument.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\GameUIDocument.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\UI\\GameUIDocument.cs", "ViewState": "AgIAAJ4AAAAAAAAAAAAvwLUAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T13:23:15.004Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "LevelData.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\GemHunterMatch\\Scripts\\LevelData.cs", "RelativeDocumentMoniker": "Assets\\GemHunterMatch\\Scripts\\LevelData.cs", "ToolTip": "F:\\Match2D\\Assets\\GemHunterMatch\\Scripts\\LevelData.cs", "RelativeToolTip": "Assets\\GemHunterMatch\\Scripts\\LevelData.cs", "ViewState": "AgIAADwAAAAAAAAAAAAawFQAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T23:16:00.012Z"}, {"$type": "Document", "DocumentIndex": 87, "Title": "GemSoundSystem.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Main\\GemSoundSystem.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Main\\GemSoundSystem.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Main\\GemSoundSystem.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Main\\GemSoundSystem.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABwAAAA1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T22:16:15.09Z"}, {"$type": "Document", "DocumentIndex": 88, "Title": "Bonus.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Bonuses\\Bonus.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Bonuses\\Bonus.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Bonuses\\Bonus.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Bonuses\\Bonus.cs", "ViewState": "AgIAAAcAAAAAAAAAAAA1wBEAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T09:43:28.624Z"}, {"$type": "Document", "DocumentIndex": 89, "Title": "BoardMatchFinderJobs.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Match3Match\\BoardMatchFinderJobs.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Match3Match\\BoardMatchFinderJobs.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Match3Match\\BoardMatchFinderJobs.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Match3Match\\BoardMatchFinderJobs.cs", "ViewState": "AgIAAIoAAAAAAAAAAAAowAUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-29T21:35:15.226Z"}, {"$type": "Document", "DocumentIndex": 90, "Title": "ShapeMatcher.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Match3Match\\ShapeMatcher.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Match3Match\\ShapeMatcher.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Match3Match\\ShapeMatcher.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Match3Match\\ShapeMatcher.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-29T21:33:48.645Z"}, {"$type": "Document", "DocumentIndex": 91, "Title": "BoardShuffleJobs.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Match3Logic\\BoardShuffleJobs.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Match3Logic\\BoardShuffleJobs.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Match3Logic\\BoardShuffleJobs.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Match3Logic\\BoardShuffleJobs.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAB4AAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-29T15:29:34.607Z"}, {"$type": "Document", "DocumentIndex": 92, "Title": "LevelLoaderJobs.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Levels\\LevelLoaderJobs.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Levels\\LevelLoaderJobs.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Levels\\LevelLoaderJobs.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Levels\\LevelLoaderJobs.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-29T15:10:30.073Z"}, {"$type": "Document", "DocumentIndex": 93, "Title": "FPSCounter.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Utils\\FPSCounter.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Utils\\FPSCounter.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Utils\\FPSCounter.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Utils\\FPSCounter.cs", "ViewState": "AgIAABkAAAAAAAAAAAAgwCMAAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-28T19:33:20.503Z"}, {"$type": "Document", "DocumentIndex": 94, "Title": "UIManager.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\UIManager.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\UI\\UIManager.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\UIManager.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\UI\\UIManager.cs", "ViewState": "AgIAACQAAAAAAAAAAAAowBAAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-28T10:28:36.684Z"}, {"$type": "Document", "DocumentIndex": 95, "Title": "ComboAnnouncer.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\ComboAnnouncer.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\UI\\ComboAnnouncer.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\ComboAnnouncer.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\UI\\ComboAnnouncer.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAuwFwAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-28T10:03:50.255Z"}, {"$type": "Document", "DocumentIndex": 96, "Title": "ComboManager.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\ComboManager.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\UI\\ComboManager.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\ComboManager.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\UI\\ComboManager.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAqwCMAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-28T06:43:20.261Z"}, {"$type": "Document", "DocumentIndex": 97, "Title": "GameUI.uss", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\UI\\Toolkit\\GameUI.uss", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\UI\\Toolkit\\GameUI.uss", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\UI\\Toolkit\\GameUI.uss", "RelativeToolTip": "Assets\\OwnMatch3\\UI\\Toolkit\\GameUI.uss", "ViewState": "AgIAADAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003000|", "WhenOpened": "2025-06-27T16:27:03.942Z"}, {"$type": "Document", "DocumentIndex": 98, "Title": "BoardJsonInit.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Match3Setup\\BoardJsonInit.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Match3Setup\\BoardJsonInit.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Match3Setup\\BoardJsonInit.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Match3Setup\\BoardJsonInit.cs", "ViewState": "AgIAAN8AAAAAAAAAAAAgwPQAAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T11:09:48.468Z"}, {"$type": "Document", "DocumentIndex": 99, "Title": "LevelData.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Level\\LevelData.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Level\\LevelData.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Level\\LevelData.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Level\\LevelData.cs", "ViewState": "AgIAABMAAAAAAAAAAAAgwBIAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T16:18:46.625Z"}, {"$type": "Document", "DocumentIndex": 100, "Title": "FrameBuilder.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Match3Setup\\FrameBuilder.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Match3Setup\\FrameBuilder.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Match3Setup\\FrameBuilder.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Match3Setup\\FrameBuilder.cs", "ViewState": "AgIAAGIAAAAAAAAAAAAQwLUAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T16:49:02.021Z"}, {"$type": "Document", "DocumentIndex": 101, "Title": "LevelLoader.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Levels\\LevelLoader.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Levels\\LevelLoader.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Levels\\LevelLoader.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Levels\\LevelLoader.cs", "ViewState": "AgIAAPEAAAAAAAAAAAAAwPUAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T11:16:27.112Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "Match3Board.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Main\\Match3Board.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Main\\Match3Board.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Main\\Match3Board.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Main\\Match3Board.cs", "ViewState": "AgIAAHUFAAAAAAAAAAAiwIcFAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T11:31:49.153Z"}, {"$type": "Document", "DocumentIndex": 102, "Title": "PointsAnnouncer.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\PointsAnnouncer.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\UI\\PointsAnnouncer.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\PointsAnnouncer.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\UI\\PointsAnnouncer.cs", "ViewState": "AgIAABUAAAAAAAAAAAAowCkAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-28T11:03:08.322Z"}, {"$type": "Document", "DocumentIndex": 103, "Title": "GameUI.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\GameUI.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\UI\\GameUI.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\UI\\GameUI.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\UI\\GameUI.cs", "ViewState": "AgIAAEIAAAAAAAAAAAA4wFkAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T10:55:11.004Z"}, {"$type": "Document", "DocumentIndex": 104, "Title": "Goal.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Goals\\Goal.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Goals\\Goal.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Goals\\Goal.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Goals\\Goal.cs", "ViewState": "AgIAAEQAAAAAAAAAAAAAAFEAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-22T23:43:42.88Z"}, {"$type": "Document", "DocumentIndex": 105, "Title": "BombXBonus.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Bonuses\\BombXBonus.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Bonuses\\BombXBonus.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Bonuses\\BombXBonus.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Bonuses\\BombXBonus.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-22T16:46:02.816Z"}, {"$type": "Document", "DocumentIndex": 106, "Title": "BombBonus.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Bonuses\\BombBonus.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Bonuses\\BombBonus.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Bonuses\\BombBonus.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Bonuses\\BombBonus.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-22T16:45:45.223Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "LineRocketBonus.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Bonuses\\LineRocketBonus.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Bonuses\\LineRocketBonus.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Bonuses\\LineRocketBonus.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Bonuses\\LineRocketBonus.cs", "ViewState": "AgIAABIBAAAAAAAAAAAcwCsBAABSAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-21T15:00:33.534Z"}, {"$type": "Document", "DocumentIndex": 107, "Title": "ObstacleSoundSystemSetup.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Match3Setup\\ObstacleSoundSystemSetup.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Match3Setup\\ObstacleSoundSystemSetup.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Match3Setup\\ObstacleSoundSystemSetup.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Match3Setup\\ObstacleSoundSystemSetup.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAJMAAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-21T12:46:17.932Z"}, {"$type": "Document", "DocumentIndex": 108, "Title": "ObstacleSoundSystem.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Match3Setup\\ObstacleSoundSystem.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Match3Setup\\ObstacleSoundSystem.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Match3Setup\\ObstacleSoundSystem.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Match3Setup\\ObstacleSoundSystem.cs", "ViewState": "AgIAAOgAAAAAAAAAAAASwDwAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-21T12:36:35.038Z"}, {"$type": "Document", "DocumentIndex": 109, "Title": "BoardInput.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Match3Input\\BoardInput.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Match3Input\\BoardInput.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Match3Input\\BoardInput.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Match3Input\\BoardInput.cs", "ViewState": "AgIAAFoAAAAAAAAAAAAtwFcAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T15:41:11.265Z"}, {"$type": "Document", "DocumentIndex": 110, "Title": "LogToFile.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Utils\\LogToFile.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Utils\\LogToFile.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Utils\\LogToFile.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Utils\\LogToFile.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T13:58:40.723Z"}, {"$type": "Document", "DocumentIndex": 111, "Title": "ObjectPooler.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Utils\\ObjectPooler.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Utils\\ObjectPooler.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Utils\\ObjectPooler.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Utils\\ObjectPooler.cs", "ViewState": "AgIAABEBAAAAAAAAAAAiwCcBAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T19:05:26.295Z"}, {"$type": "Document", "DocumentIndex": 112, "Title": "GemNew.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Gems\\GemNew.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Gems\\GemNew.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Gems\\GemNew.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Gems\\GemNew.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T15:49:15.444Z"}, {"$type": "Document", "DocumentIndex": 113, "Title": "ObstacleTileData.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Match3Obstacles\\ObstacleTileData.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Match3Obstacles\\ObstacleTileData.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Match3Obstacles\\ObstacleTileData.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Match3Obstacles\\ObstacleTileData.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T14:01:12.83Z"}, {"$type": "Document", "DocumentIndex": 114, "Title": "FishBonus.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Bonuses\\FishBonus.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Bonuses\\FishBonus.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Bonuses\\FishBonus.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Bonuses\\FishBonus.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T16:43:31.147Z"}, {"$type": "Document", "DocumentIndex": 115, "Title": "ColorBonus.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Bonuses\\ColorBonus.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Bonuses\\ColorBonus.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Bonuses\\ColorBonus.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Bonuses\\ColorBonus.cs", "ViewState": "AgIAAIQAAAAAAAAAAAAuwHwAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T16:38:24.023Z"}, {"$type": "Document", "DocumentIndex": 116, "Title": "ObstacleHandler.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Match3Obstacles\\ObstacleHandler.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Match3Obstacles\\ObstacleHandler.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Match3Obstacles\\ObstacleHandler.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Match3Obstacles\\ObstacleHandler.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAgwCQAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T11:21:12.965Z"}, {"$type": "Document", "DocumentIndex": 117, "Title": "BoardUtils.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Match3Setup\\BoardUtils.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Match3Setup\\BoardUtils.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Match3Setup\\BoardUtils.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Match3Setup\\BoardUtils.cs", "ViewState": "AgIAAH0AAAAAAAAAAADgv5AAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T11:19:11.917Z"}, {"$type": "Document", "DocumentIndex": 118, "Title": "BoardMatchResolver.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Match3Match\\BoardMatchResolver.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Match3Match\\BoardMatchResolver.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Match3Match\\BoardMatchResolver.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Match3Match\\BoardMatchResolver.cs", "ViewState": "AgIAACwAAAAAAAAAAAAgwIEAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T11:12:01.278Z"}, {"$type": "Document", "DocumentIndex": 119, "Title": "BoardMatchFinder.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Match3Match\\BoardMatchFinder.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Match3Match\\BoardMatchFinder.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Match3Match\\BoardMatchFinder.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Match3Match\\BoardMatchFinder.cs", "ViewState": "AgIAAGAAAAAAAAAAAAAvwHYAAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T11:07:33.49Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "BoardLogic.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Match3Logic\\BoardLogic.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\Match3Logic\\BoardLogic.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\Match3Logic\\BoardLogic.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\Match3Logic\\BoardLogic.cs", "ViewState": "AgIAAFECAAAAAAAAAAAqwGQCAAA0AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T11:05:19.125Z"}, {"$type": "Document", "DocumentIndex": 120, "Title": "ObstacleNew.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\ObstacleNew.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\ObstacleNew.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\ObstacleNew.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\ObstacleNew.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAQwBYAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T08:37:19.897Z"}, {"$type": "Document", "DocumentIndex": 121, "Title": "MatchInfo.cs", "DocumentMoniker": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\MatchInfo.cs", "RelativeDocumentMoniker": "Assets\\OwnMatch3\\Scripts\\MatchInfo.cs", "ToolTip": "F:\\Match2D\\Assets\\OwnMatch3\\Scripts\\MatchInfo.cs", "RelativeToolTip": "Assets\\OwnMatch3\\Scripts\\MatchInfo.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T20:21:30.201Z"}]}]}]}