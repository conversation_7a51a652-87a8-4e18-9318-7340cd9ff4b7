﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <LangVersion>9.0</LangVersion>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.20506</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <RootNamespace></RootNamespace>
    <ProjectGuid>{39A1ED07-2DA9-E77E-E029-E4047CBFA848}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>OwnMatch3</AssemblyName>
    <TargetFrameworkVersion>v4.7.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup>
    <NoWarn>0169;USG0001</NoWarn>
    <DefineConstants>UNITY_6000_2_0;UNITY_6000_2;UNITY_6000;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;UNITY_2023_1_OR_NEWER;UNITY_2023_2_OR_NEWER;UNITY_2023_3_OR_NEWER;UNITY_6000_0_OR_NEWER;UNITY_6000_1_OR_NEWER;UNITY_6000_2_OR_NEWER;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_UNITY_CONSENT;ENABLE_UNITY_CLOUD_IDENTIFIERS;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_ENGINE_CODE_STRIPPING;ENABLE_ONSCREEN_KEYBOARD;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;ENABLE_MARSHALLING_TESTS;ENABLE_VIDEO;ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;ENABLE_ACCESSIBILITY;TEXTCORE_1_0_OR_NEWER;EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED;PLATFORM_ANDROID;UNITY_ANDROID;UNITY_ANDROID_API;ENABLE_EGL;ENABLE_NETWORK;ENABLE_RUNTIME_GI;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_CLOUD_SERVICES_ENGINE_DIAGNOSTICS;ENABLE_FIREBASE_IDENTIFIERS;UNITY_CAN_SHOW_SPLASH_SCREEN;UNITY_HAS_GOOGLEVR;UNITY_HAS_TANGO;ENABLE_SPATIALTRACKING;ENABLE_ETC_COMPRESSION;PLATFORM_EXTENDS_VULKAN_DEVICE;PLATFORM_HAS_MULTIPLE_SWAPCHAINS;UNITY_ANDROID_SUPPORTS_SHADOWFILES;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;PLATFORM_EXTENDS_VULKAN_PIPELINE_CACHE;PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS;PLATFORM_HAS_ADDITIONAL_API_CHECKS;PLATFORM_HAS_GRAPHICS_JOBS_SUPPORT_CHECK_OVERRIDE;PLATFORM_IMPLEMENTS_INSIGHTS_ANR;ENABLE_ANDROID_ADVERTISING_IDS;PLATFORM_HAS_BUGGY_MSAA_RESOLVE;ENABLE_UNITYADS_RUNTIME;UNITY_UNITYADS_API;ENABLE_MONO;NET_STANDARD_2_0;NET_STANDARD;NET_STANDARD_2_1;NETSTANDARD;NETSTANDARD2_1;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER;PRIME_TWEEN_SAFETY_CHECKS;LEVELPLAY_DEPENDENCIES_INSTALLED;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\bin\Debug\</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>Temp\bin\Release\</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <NoConfig>true</NoConfig>
    <NoStdLib>true</NoStdLib>
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
    <ImplicitlyExpandNETStandardFacades>false</ImplicitlyExpandNETStandardFacades>
    <ImplicitlyExpandDesignTimeFacades>false</ImplicitlyExpandDesignTimeFacades>
  </PropertyGroup>
  <PropertyGroup>
    <ProjectTypeGuids>{E097FAD1-6243-4DAD-9C02-E9B9EFC3FFC1};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.23</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>Legacy</UnityProjectGeneratorStyle>
    <UnityProjectType>Game:1</UnityProjectType>
    <UnityBuildTarget>Android:13</UnityBuildTarget>
    <UnityVersion>6000.2.0b9</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="D:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\Extensions\Microsoft\Visual Studio Tools for Unity\Analyzers\Microsoft.Unity.Analyzers.dll" />
    <Analyzer Include="F:\Unity Installs\6000.2.0b9\Editor\Data\Tools\Unity.SourceGenerators\Unity.SourceGenerators.dll" />
    <Analyzer Include="F:\Unity Installs\6000.2.0b9\Editor\Data\Tools\Unity.SourceGenerators\Unity.Properties.SourceGenerator.dll" />
    <Analyzer Include="F:\Unity Installs\6000.2.0b9\Editor\Data\Tools\Unity.SourceGenerators\Unity.UIToolkit.SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Assets\OwnMatch3\Scripts\UI\MovesWarningAnimation.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Security\UpdateGame.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\Particles\ParticleElementPropertyTester.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\Tools\DebugUnityFieldSerialization.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\EmailManager.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Match3Match\BoardMatchFinder.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\Tools\SecuredTypePatcher.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Memory\SecuredTypes\SecuredUInt.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\Tools\SecuredTypeInitializer.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\EditorExtensions.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\MoveToNextScene.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\PropertyDrawers\SecuredQuaternionDrawer.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Match3Match\BoardMatchFinderJobs.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Memory\SecuredTypes\SecuredVector2.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\PreGamePopupSetup.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Teleport\TeleportDetector.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Boosters\BoosterConfiguration.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Main\Match3Board.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Main\LevelCompletionHandler.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Teleport\TeleportTargetChecker.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Bonuses\BonusActivityTracker.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Injection\InjectionWarnings.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Memory\SecuredTypes\SecuredString.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\PropertyDrawers\SecuredBoolDrawer.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Time\TimeWarnings.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\Tools\AutoResetSecuredTypesOnReload.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Memory\SecuredTypes\SecuredChar.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Utils\CameraSetupHelper.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\AssembliesWhitelist.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Utils\DebugManager.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\PropertyDrawers\SecuredPropertyDrawer.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\PropertyDrawers\SecuredColor32Drawer.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Avatars\AvatarManager.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Main\BoardPerformanceConfig.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Memory\SecuredTypes\SecuredULong.cs" />
    <Compile Include="Assets\OwnMatch3\UI\Custom UI Elements\StarRatingElement.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Memory\SecuredTypes\SecuredIntAttribute.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Debug\LevelCountTest.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\IShieldModuleConfig.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Gems\GemNew.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Utils\ObjectPoolerMonitor.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\Particles\ParticleElementDemo.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Bonuses\BombXBonus.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\UIUtils.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\AI\AdaptiveComponents.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Match3Obstacles\ObstacleHandler.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\MissionsUIController.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Editor\DebugMigrationWindow.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Utils\CameraManager.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Main\AudioManager.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\PropertyDrawers\SecuredVector4Drawer.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\PropertyDrawers\SecuredColorDrawer.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Security\EncryptionKeyManager.cs" />
    <Compile Include="Assets\OwnMatch3\PlayerInput.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\PropertyDrawers\SecuredVector2Drawer.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\ObstacleNew.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\GameSaves\Payloads\SaveGamePayloads.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Levels\LevelLoaderJobs.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\GemAttractorUI.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\AI\LevelDataConverter.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Injection\InjectionScanner.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Levels\LevelSetup.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Memory\SecuredTypes\SecuredSByte.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Main\BoardPerformanceSimple.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\GameUIDocument.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Examples\BonusGemSystemExample.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\Tools\UnityFieldInitializationFix.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\AI\LevelPatterns.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\GameSaves\SavesWarnings.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Security\GameSecurity.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Editor\ObjectPoolerAutoOptimizer.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\CurrencyAnimationManager.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Shop\ShopDataStructures.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Match3Match\ShapeMatcher.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Editor\ObjectPoolerEditorWindow.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Utils\ModuleManager.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Match3Setup\BoardJsonInit.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\Tools\ForceInspectorRefresh.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Bonuses\RocketExplosionEffect.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Editor\LevelPreviewComponent.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\PropertyDrawers\SecuredVector3Drawer.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Demo\Scripts\Payloads\UIPayloads.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Memory\SecuredTypes\SecuredColor32.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\Tools\QuickSecuredTypeConverter.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\AI\AILevelGeneratorIntegration.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Bonuses\FishBonus.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\GameShieldWizzard.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Editor\CreateAIConfigAssets.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Demo\Scripts\Payloads\EnemyPayloads.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Bonuses\LineRendererExtensions.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Demo\Scripts\UI\DetectionView.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Memory\SecuredTypes\SecuredBool.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\AI\DifficultyCalculator.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Editor\ObjectPoolerToolbar.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\MenuUIController.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Match3Setup\ObstacleSoundSystemSetup.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\AI\AILevelGenerator.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Examples\StarAnimationTester.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Memory\MemoryProtector.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Examples\BonusGemSystemExampleAdvanced.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\Tools\SafeSecuredTypeConverter.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Constants\GeneralConstants.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Security\BlowFishWrapper.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Match3Setup\BoardUtils.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Editor\ObjectPoolerSettings.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\EmailUIController.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\Particles\ParticleElementPositionTest.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\Tools\SecuredTypeReloadFixer.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\MatchInfo.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\AI\LevelValidator.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Shop\ShopManager.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\ProgressDisplayUI.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Match3Setup\ObstacleSoundSystem.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Demo\Scripts\UI\Elements\CaptchaButton.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Testing\TestingSecuredTypes.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Utils\Generator.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\ResponsiveUIHelper.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\GameShieldStyles.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Monetization\AdsGameIntegration.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\Postprocessor.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\AI\AdaptiveConfig.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Testing\SaveSystemTester.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Demo\Scripts\UI\CaptchaView.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Main\GameStateManager.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Setup\BonusGemSystemSetup.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Main\ScoreManager.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Bonuses\GemDestructionEffect.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\GameUI.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Memory\SecuredTypes\SecuredShort.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Editor\SimpleCameraManagerEditor.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Testing\SaveSystemDebugger.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Payloads\LifecyclePayloads.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Match3Obstacles\ObstacleTileData.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Editor\ObjectPoolerGizmos.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Level\LevelData.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\GameSaves\ISaveObject.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Demo\Scripts\UI\BaseView.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Main\BoardPerformanceOptimizations.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Editor\EditShapeMatch.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\AI\AIGenerationStructures.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Effects\BonusGemEffects.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\MissionsManager.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Web\SecuredRequest.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\GameSaves\SecuredSaves.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Demo\Scripts\UI\DialogView.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Time\TimeProtector.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Levels\LevelLoader.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Monetization\AdsSetupExample.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\GoalDisplayConfiguration.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Teleport\TeleportWarnings.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Demo\Scripts\Payloads\PlayerPayloads.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Memory\SecuredTypes\SecuredLong.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Memory\SecuredTypes\SecuredFloat.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Editor\EncryptionKeyGeneratorWindow.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Editor\MatchShapeValidator.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Captcha\RequestCaptchaPayload.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\PropertyDrawers\SecuredIntDrawer.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\Tools\SecuredTypeConverterDocumentation.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Utils\DebugMigrationHelper.cs" />
    <Compile Include="Assets\OwnMatch3\UI\Custom UI Elements\CurvedProgressBar.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\PointsAnnouncer.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\Particles\SimpleParticleController.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\GameSaves\SaveGameData.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Demo\Scripts\GameInstaller.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Main\CloudSaveExample.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Main\BonusGemManager.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\PropertyDrawers\SecuredStringDrawer.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Avatars\AvatarConfiguration.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Goals\Goal.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\GameShield.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Memory\SecuredTypes\SecuredByte.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\AllowedAssembly.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Utils\DebugSettings.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Utils\LogToFile.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Memory\SecuredTypes\SecuredUShort.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Editor\ObjectPoolerGenericExamples.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\ScoreProgressBarController.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Utils\ObjectPoolerLinqUtils.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\Tools\UltimateSecuredTypeConverter.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Utils\EnumerableExtensions.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\SpeedHack\SpeedHackWarnings.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\UIManager.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\EndGamePopupController.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Shop\ShopConfiguration.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\ComboAnnouncer.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Memory\SecuredTypes\SecuredQuaternion.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\Particles\ParticleElementSizeTest.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Memory\SecuredTypes\SecuredVector3.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Match3Logic\BoardLogic.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Match3Match\BoardMatchFinderOptimized.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Bonuses\BombBonus.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\ModuleInfo.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Demo\Scripts\Payloads\CameraPayloads.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\StageProgressBarExample.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Monetization\AdsManager.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Match3Setup\FrameBuilder.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Main\HintMove.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Demo\Scripts\Enums\BaseState.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\Tools\SourceCodeDefaultValueParser.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\ComboManager.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\CloudSaveStatusUI.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Captcha\RewardedCaptchaData.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\Tools\SimpleSecuredTypeFix.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\WallHack\WallHackProtector.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\DailyRewardsManager.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\AI\AILevelIntegration.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Bonuses\Bonus.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\WizzardTab.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\SpeedHack\SpeedHackDetector.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\AI\LevelAnalyzer.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\GameShieldConfig.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\AI\PatternMatcher.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\StageProgressBarDebug.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Utils\FPSCounter.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\StageProgressBar.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Security\GetTimeNet.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Utils\ObjectPooler.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Demo\Scripts\UI\ReportingView.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\IShieldModule.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Memory\SecuredTypes\ImprovedSecuredInt.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Bonuses\LineRocketBonus.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Security\GetTimeNetInitializer.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Reporter\ReportData.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\CurrencyAttractorUI.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\WallHack\WallhackMessages.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Match3Logic\BoardShuffleJobs.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Memory\SecuredTypes\SecuredInt.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\Tools\SmartSecuredTypeConverter.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\Tools\SecuredTypeBatchConverter.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Editor\ModernAILevelGeneratorWindow.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\Particles\ParticleElementTester.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Injection\AllowedAssembly.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Memory\SecuredTypes\SecuredColor.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Demo\Scripts\UI\WelcomeView.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\StarCalculationTest.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Monetization\AdReward.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Demo\Scripts\Controllers\EnemyController.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\PreGamePopupController.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Match3Input\BoardInput.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\Tools\QuickDataFix.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\Tools\SecuredTypeSettingsWindow.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\LevelSelectionUI.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Bonuses\RocketTrailEffect.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Constants\GeneralStrings.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Main\SaveSystemIntegration.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Match3Match\BoardMatchResolver.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\Particles\ParticleElement.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Memory\SecuredTypes\SecuredVector4.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Demo\Scripts\Controllers\CameraController.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Boosters\BoosterManager.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Payloads\SecurityPayloads.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\Tools\SecuredTypeConverter.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\AI\LevelLoaderExtensions.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Testing\CurrencyTestController.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Memory\MemoryWarnings.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Editor\VideoFrameExtractorWindow.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Levels\LevelJson.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Web\WebRequestData.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Main\GameProgressManager.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\UI\ScoreProgressBarExample.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Editor\GameProgressManagerEditor.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Match3Match\BoardMatchFinderAsync.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\AI\AdaptiveGenerationSystem.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Bonuses\ColorBonus.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Monetization\AdConfig.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Components\SecuredTypeAutoFix.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\PropertyDrawers\SecuredFloatDrawer.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Memory\SecuredTypes\SecuredDecimal.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Monetization\AdsIntegrationHelper.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Editor\InjectionDetectorGlobal.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Demo\Scripts\UI\InGameView.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Demo\Scripts\Controllers\PlayerController.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Reporter\ReporterWorker.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\Main\GemSoundSystem.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Modules\Captcha\RewardedCaptcha.cs" />
    <Compile Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Core\Reporter\ReportingPayload.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Assets\OwnMatch3\UI\Toolkit\DailyRewards.uss" />
    <None Include="Assets\OwnMatch3\UI\Toolkit\AvatarShopItem.uxml" />
    <None Include="Assets\OwnMatch3\UI\Toolkit\ButtonIcon.uss" />
    <None Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Demo\UI\UI Document\UI Dialogs\DialogueViewTK.uxml" />
    <None Include="Assets\OwnMatch3\UI\Toolkit\MissionItem.uxml" />
    <None Include="Assets\OwnMatch3\UI\Toolkit\ShopPanel.uxml" />
    <None Include="Assets\OwnMatch3\UI\DalilyReward Items\ItemReward.uxml" />
    <None Include="Assets\OwnMatch3\UI\Toolkit\ParticleElement.uss" />
    <None Include="Assets\OwnMatch3\UI\Toolkit\ButtonAnimations.uss" />
    <None Include="Assets\OwnMatch3\UI\Levels UI\ScrollFixes.uss" />
    <None Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Demo\UI\UI Document\UI Dialogs\InGameUITK.uxml" />
    <None Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Demo\UI\UI Document\USS\TextField.uss" />
    <None Include="Assets\OwnMatch3\UI\Levels UI\FoldoutLogic.uss" />
    <None Include="Assets\OwnMatch3\Scripts\Editor\GameProgressManagerEditor.uss" />
    <None Include="Assets\OwnMatch3\UI\ShowTextScreen\ShowPoints.uxml" />
    <None Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Demo\UI\UI Document\UI Dialogs\WelcomeViewTK.uxml" />
    <None Include="Assets\OwnMatch3\UI\Toolkit\MissionsPanel.uxml" />
    <None Include="Assets\OwnMatch3\UI\Toolkit\EmailPanel.uxml" />
    <None Include="Assets\OwnMatch3\UI\Toolkit\ShopPanel.uss" />
    <None Include="Assets\OwnMatch3\UI\Toolkit\MenuUI.uxml" />
    <None Include="Assets\OwnMatch3\UI\Levels UI\ItemLevelTemplate.uxml" />
    <None Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Demo\UI\UI Document\UI Dialogs\ReportViewTK.uxml" />
    <None Include="Assets\OwnMatch3\UI\Levels UI\StageProgressBar.uss" />
    <None Include="Assets\OwnMatch3\UI\Toolkit\BoosterShopItem.uxml" />
    <None Include="Assets\OwnMatch3\UI\Toolkit\PreGamePopup.uxml" />
    <None Include="Assets\OwnMatch3\Scripts\UI\Particles\ParticleElement.uss" />
    <None Include="Assets\OwnMatch3\Render Textures\alphaBlackURP.shader" />
    <None Include="Assets\OwnMatch3\UI\Toolkit\GameUI.uss" />
    <None Include="Assets\OwnMatch3\OwnMatch3.asmdef" />
    <None Include="Assets\OwnMatch3\Scripts\DevsDaddy\GameShield\Demo\UI\UI Document\UI Dialogs\DetectionViewTK.uxml" />
    <None Include="Assets\OwnMatch3\UI\Levels UI\GeneralLevelText.uss" />
    <None Include="Assets\OwnMatch3\Scripts\Editor\ModernAILevelGenerator.uss" />
    <None Include="Assets\OwnMatch3\UI\Toolkit\GameUI.uxml" />
    <None Include="Assets\OwnMatch3\Scripts\Editor\CameraManagerEditor.uss" />
    <None Include="Assets\OwnMatch3\UI\ShowTextScreen\ShowTextScreenCenter.uxml" />
    <None Include="Assets\OwnMatch3\UI\Levels UI\ItemLevel.uxml" />
    <None Include="Assets\OwnMatch3\UI\Toolkit\EmailItem.uxml" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.ContentLoadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GraphicsStateCollectionSerializerModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HierarchyCoreModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.HierarchyCoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IdentifiersModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.IdentifiersModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputForUIModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.InputForUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InsightsModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.InsightsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MarshallingModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.MarshallingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MultiplayerModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ShaderVariantAnalyticsModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.ShaderVariantAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConsentModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConsentModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AccessibilityModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AdaptivePerformanceModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.AdaptivePerformanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.BuildProfileModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.BuildProfileModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.ClothModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreBusinessMetricsModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.CoreBusinessMetricsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.EditorToolbarModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EmbreeModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.EmbreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GIModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphicsStateCollectionSerializerModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridAndSnapModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.GridAndSnapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.MultiplayerModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Physics2DModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PhysicsModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.PresetsUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PropertiesModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SafeModeModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.SafeModeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.SceneViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.ShaderFoundryModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.ShaderFoundryModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SketchUpModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.SketchUpModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteMaskModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteShapeModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SubstanceModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TerrainModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextRenderingModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TilemapModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TreeModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.TreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIAutomationModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.UIAutomationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UmbraModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VFXModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VideoModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.XRModule">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEngine\UnityEditor.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Graphs">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\Managed\UnityEditor.Graphs.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.WindowsStandalone.Extensions">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\PlaybackEngines\WindowsStandaloneSupport\UnityEditor.WindowsStandalone.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Android.Extensions">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\PlaybackEngines\AndroidPlayer\UnityEditor.Android.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Google.PackageManagerResolver">
      <HintPath>Assets\ExternalDependencyManager\Editor\1.2.182\Google.PackageManagerResolver.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.YamlDotNet">
      <HintPath>Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Google.VersionHandler">
      <HintPath>Assets\ExternalDependencyManager\Editor\Google.VersionHandler.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>Library\PackageCache\com.unity.collections@d49facba0036\Unity.Collections.LowLevel.ILSupport\Unity.Collections.LowLevel.ILSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>Library\PackageCache\com.unity.ext.nunit@031a54704bff\net40\unity-custom\nunit.framework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Google.VersionHandlerImpl">
      <HintPath>Assets\ExternalDependencyManager\Editor\1.2.182\Google.VersionHandlerImpl.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="unityplastic">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\unityplastic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Plastic.Antlr3.Runtime">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Unity.Plastic.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Google.IOSResolver">
      <HintPath>Assets\ExternalDependencyManager\Editor\1.2.182\Google.IOSResolver.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Plastic.Newtonsoft.Json">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Unity.Plastic.Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="log4netPlastic">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\log4netPlastic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.IonicZip">
      <HintPath>Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Hashing">
      <HintPath>Library\PackageCache\com.unity.collections@d49facba0036\Unity.Collections.Tests\System.IO.Hashing\System.IO.Hashing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Antlr3.Runtime">
      <HintPath>Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Flow\Dependencies\NCalc\Unity.VisualScripting.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe">
      <HintPath>Assets\Plugins\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Google.JarResolver">
      <HintPath>Assets\ExternalDependencyManager\Editor\1.2.182\Google.JarResolver.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>Library\PackageCache\com.unity.nuget.newtonsoft-json@74deb55db2a0\Runtime\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="AiEditorToolsSdk">
      <HintPath>Library\PackageCache\com.unity.ai.toolkit@97783faabe3b\Modules\Sdk\Editor\AiEditorToolsSdk.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.TextureAssets">
      <HintPath>Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Editor\VisualScripting.Core\EditorAssetResources\Unity.VisualScripting.TextureAssets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Mono.Cecil">
      <HintPath>Library\PackageCache\com.unity.nuget.mono-cecil@d78732e851eb\Mono.Cecil.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Types">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Types.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Gradle">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Gradle.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\ref\2.1.0\netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Buffers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tracing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.DispatchProxy.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.ILGeneration.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.Lightweight.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\Extensions\2.0.0\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Net.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Web">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ServiceModel.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Web">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Windows">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Windows.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Serialization">
      <HintPath>F:\Unity Installs\6000.2.0b9\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TextMeshPro">
      <HintPath>Library\ScriptAssemblies\Unity.TextMeshPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Mathematics">
      <HintPath>Library\ScriptAssemblies\Unity.Mathematics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.InputSystem">
      <HintPath>Library\ScriptAssemblies\Unity.InputSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="PrimeTween.Runtime">
      <HintPath>Library\ScriptAssemblies\PrimeTween.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Burst">
      <HintPath>Library\ScriptAssemblies\Unity.Burst.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections">
      <HintPath>Library\ScriptAssemblies\Unity.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.SpriteShape.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.2D.SpriteShape.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.LevelPlay">
      <HintPath>Library\ScriptAssemblies\Unity.LevelPlay.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UI">
      <HintPath>Library\ScriptAssemblies\UnityEditor.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UI">
      <HintPath>Library\ScriptAssemblies\UnityEngine.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="CFXRRuntime.csproj">
      <Project>{78775993-2A4A-5391-2DC4-B170A3C41EBA}</Project>
      <Name>CFXRRuntime</Name>
    </ProjectReference>
    <ProjectReference Include="ZString.csproj">
      <Project>{216120E2-378D-4065-96CD-9AD0815B3C43}</Project>
      <Name>ZString</Name>
    </ProjectReference>
    <ProjectReference Include="UniTask.csproj">
      <Project>{A710FFDA-CD8B-6D9A-5E47-5A80B29141EF}</Project>
      <Name>UniTask</Name>
    </ProjectReference>
    <ProjectReference Include="Google.Play.Games.csproj">
      <Project>{C51E3766-0CA4-E46A-A4CB-A2A1EE7D2D72}</Project>
      <Name>Google.Play.Games</Name>
    </ProjectReference>
    <ProjectReference Include="Google.Play.AppUpdate.csproj">
      <Project>{7E9DB5DD-DBA7-4E08-E9BE-8DCDAAB35C16}</Project>
      <Name>Google.Play.AppUpdate</Name>
    </ProjectReference>
    <ProjectReference Include="Google.Play.Common.csproj">
      <Project>{B6267C46-9D6D-9011-D19A-1A465960B180}</Project>
      <Name>Google.Play.Common</Name>
    </ProjectReference>
    <ProjectReference Include="CryptoLibrary.csproj">
      <Project>{22D3DF60-6A81-6A5C-FE97-C7FCC600BC02}</Project>
      <Name>CryptoLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="EventFramework.csproj">
      <Project>{C1B64A86-57FE-2331-A6B2-AFB0A1E75077}</Project>
      <Name>EventFramework</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="GenerateTargetFrameworkMonikerAttribute" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
