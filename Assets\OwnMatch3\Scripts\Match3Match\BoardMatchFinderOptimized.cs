using Unity.Collections;
using Unity.Jobs;
using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using Cysharp.Threading.Tasks;
using OwnMatch3.Utils;

/// <summary>
/// Performance-optimized match finder for Match3Board
/// Addresses the 35.8% frame time issue in ClearMatchesAsync
/// </summary>
public partial class Match3Board
{
    // Optimized match finder with async job completion
    private MatchFinderOptimized _optimizedMatchFinder;
    
    /// <summary>
    /// Initialize the optimized match finder
    /// </summary>
    private void InitializeOptimizedMatchFinder()
    {
        if (_optimizedMatchFinder == null)
        {
            _optimizedMatchFinder = new MatchFinderOptimized(this);
        }
    }
    
    /// <summary>
    /// Optimized match detection with async job completion
    /// </summary>
    public async UniTask<List<MatchInfo>> DetectAllMatchesAsyncOptimized(bool checkBonusShapes = true)
    {
        if (!enablePerformanceOptimizations || !useAsyncJobs)
        {
            return DetectAllMatches(checkBonusShapes);
        }
        
        InitializeOptimizedMatchFinder();
        return await _optimizedMatchFinder.DetectMatchesInLayoutAsync(gems);
    }
}

/// <summary>
/// Optimized match finder with async job completion
/// </summary>
public class MatchFinderOptimized
{
    private readonly Match3Board board;
    private NativeArray<Vector3Int> gemPositions;
    private NativeArray<int> gemTypes;
    private NativeHashMap<Vector3Int, int> gemGrid;
    private NativeList<MatchFinder.MatchInfoJobData> matches;
    private NativeList<Vector3Int> matchedPositions;
    private NativeArray<MatchFinder.ShapeJobData> shapes;
    private NativeArray<Vector3Int> shapeCells;
    
    public MatchFinderOptimized(Match3Board board, int initialCapacity = 200)
    {
        this.board = board;
        gemPositions = new NativeArray<Vector3Int>(initialCapacity, Allocator.Persistent);
        gemTypes = new NativeArray<int>(initialCapacity, Allocator.Persistent);
        gemGrid = new NativeHashMap<Vector3Int, int>(initialCapacity, Allocator.Persistent);
        matches = new NativeList<MatchFinder.MatchInfoJobData>(initialCapacity, Allocator.Persistent);
        matchedPositions = new NativeList<Vector3Int>(initialCapacity, Allocator.Persistent);
    }
    
    /// <summary>
    /// Cache shape data for optimized matching
    /// </summary>
    public void CacheShapeData(List<MatchShape> matchShapes, List<BonusGemData> bonusGems)
    {
        if (shapes.IsCreated) shapes.Dispose();
        if (shapeCells.IsCreated) shapeCells.Dispose();

        var shapeDataList = new List<MatchFinder.ShapeJobData>();
        var cellsList = new List<Vector3Int>();

        int shapeIdCounter = 0;

        foreach (var shape in matchShapes)
        {
            if (shape?.Cells == null || shape.Cells.Count == 0) continue;

            var shapeData = new MatchFinder.ShapeJobData
            {
                BounsGemId = -1,
                ShapeId = shapeIdCounter++,
                CanRotate = shape.CanRotate ? 1 : 0,
                CanMirror = shape.CanMirror ? 1 : 0,
                CellsStartIndex = cellsList.Count,
                CellCount = shape.Cells.Count,
                Cells90RotStartIndex = cellsList.Count + shape.Cells.Count,
                Cells180RotStartIndex = cellsList.Count + shape.Cells.Count + shape.Cell90Rot.Count,
                Cells270RotStartIndex = cellsList.Count + shape.Cells.Count + shape.Cell90Rot.Count + shape.Cell180Rot.Count,
                CellsHMirrorStartIndex = cellsList.Count + shape.Cells.Count + shape.Cell90Rot.Count + shape.Cell180Rot.Count + shape.Cell270Rot.Count,
                CellsVMirrorStartIndex = cellsList.Count + shape.Cells.Count + shape.Cell90Rot.Count + shape.Cell180Rot.Count + shape.Cell270Rot.Count + shape.CellHMirror.Count
            };

            shapeDataList.Add(shapeData);
            cellsList.AddRange(shape.Cells);
            cellsList.AddRange(shape.Cell90Rot);
            cellsList.AddRange(shape.Cell180Rot);
            cellsList.AddRange(shape.Cell270Rot);
            cellsList.AddRange(shape.CellHMirror);
            cellsList.AddRange(shape.CellVMirror);
        }

        shapes = new NativeArray<MatchFinder.ShapeJobData>(shapeDataList.ToArray(), Allocator.Persistent);
        shapeCells = new NativeArray<Vector3Int>(cellsList.ToArray(), Allocator.Persistent);
    }
    
    /// <summary>
    /// Detect matches asynchronously to prevent frame drops
    /// </summary>
    public async UniTask<List<MatchInfo>> DetectMatchesInLayoutAsync(Dictionary<Vector3Int, GemNew> gems)
    {
        int gemCount = gems.Count;
        if (gemCount == 0 || !shapes.IsCreated) 
        {
            return new List<MatchInfo>();
        }
        
        // Resize arrays if needed
        if (gemPositions.Length < gemCount)
        {
            ResizeArrays(gemCount);
        }
        
        // Clear collections
        gemGrid.Clear();
        matches.Clear();
        matchedPositions.Clear();
        
        // Populate gem data
        int i = 0;
        int skippedGems = 0;
        foreach (var kvp in gems)
        {
            if (board.IsGemUnmatchableByIce(kvp.Key))
            {
                skippedGems++;
                continue;
            }
            
            gemPositions[i] = kvp.Key;
            gemTypes[i] = (int)kvp.Value.GemType;
            gemGrid.Add(kvp.Key, (int)kvp.Value.GemType);
            i++;
        }
        
        // Create and schedule job
        var job = new Match3Board.DetectMatchesJob
        {
            GemPositions = gemPositions.GetSubArray(0, i),
            GemTypes = gemTypes.GetSubArray(0, i),
            GemGrid = gemGrid,
            Shapes = shapes,
            ShapeCells = shapeCells,
            Matches = matches,
            MatchedPositions = matchedPositions
        };
        
        JobHandle handle = job.Schedule();
        
        // Wait for job completion asynchronously
        while (!handle.IsCompleted)
        {
            await UniTask.Yield();
        }
        
        handle.Complete();
        
        return ProcessJobResults();
    }
    
    /// <summary>
    /// Resize arrays when needed
    /// </summary>
    private void ResizeArrays(int newSize)
    {
        if (gemPositions.IsCreated) gemPositions.Dispose();
        if (gemTypes.IsCreated) gemTypes.Dispose();
        if (gemGrid.IsCreated) gemGrid.Dispose();
        
        gemPositions = new NativeArray<Vector3Int>(newSize, Allocator.Persistent);
        gemTypes = new NativeArray<int>(newSize, Allocator.Persistent);
        gemGrid = new NativeHashMap<Vector3Int, int>(newSize, Allocator.Persistent);
    }
    
    /// <summary>
    /// Process job results into MatchInfo objects
    /// </summary>
    private List<MatchInfo> ProcessJobResults()
    {
        var result = new List<MatchInfo>();

        for (int i = 0; i < matches.Length; i++)
        {
            var matchData = matches[i];
            var positions = new List<Vector3Int>();

            for (int j = matchData.MatchStartIndex; j < matchData.MatchStartIndex + matchData.MatchLength; j++)
            {
                if (j < matchedPositions.Length)
                {
                    positions.Add(matchedPositions[j]);
                }
            }

            if (positions.Count >= 3)
            {
                // Get shape and bonus data if available
                MatchShape shape = matchData.ShapeId != -1 ? GetShapeById(matchData.ShapeId) : null;
                BonusGemData bonus = matchData.BonusGemId != -1 ? GetBonusGemById(matchData.BonusGemId) : null;

                var matchInfo = new MatchInfo(positions, (GemType)matchData.GemType, shape, bonus);
                result.Add(matchInfo);
            }
        }

        return result;
    }
    
    /// <summary>
    /// Get shape by ID (placeholder - implement based on your caching system)
    /// </summary>
    private MatchShape GetShapeById(int shapeId)
    {
        // This should be implemented to return the actual shape
        // For now, return null to avoid compilation errors
        _ = shapeId; // Suppress unused parameter warning
        return null;
    }

    /// <summary>
    /// Get bonus gem data by ID (placeholder - implement based on your bonus system)
    /// </summary>
    private BonusGemData GetBonusGemById(int bonusId)
    {
        // This should be implemented to return the actual bonus data
        // For now, return null to avoid compilation errors
        _ = bonusId; // Suppress unused parameter warning
        return null;
    }
    
    /// <summary>
    /// Dispose native collections
    /// </summary>
    public void Dispose()
    {
        if (gemPositions.IsCreated) gemPositions.Dispose();
        if (gemTypes.IsCreated) gemTypes.Dispose();
        if (gemGrid.IsCreated) gemGrid.Dispose();
        if (matches.IsCreated) matches.Dispose();
        if (matchedPositions.IsCreated) matchedPositions.Dispose();
        if (shapes.IsCreated) shapes.Dispose();
        if (shapeCells.IsCreated) shapeCells.Dispose();
    }
}

/// <summary>
/// Extension methods for optimized match detection
/// </summary>
public static class MatchFinderExtensions
{
    /// <summary>
    /// Get match detection performance statistics
    /// </summary>
    public static (int gemCount, int matchCount, float processingTime) GetMatchDetectionStats(this Match3Board board)
    {
        // Implementation for performance tracking
        return (board.gems.Count, 0, 0f);
    }
    
    /// <summary>
    /// Validate match detection results
    /// </summary>
    public static bool ValidateMatchResults(this List<MatchInfo> matches)
    {
        foreach (var match in matches)
        {
            if (match.positions == null || match.positions.Count < 3)
            {
                return false;
            }
        }
        return true;
    }
}
