# C/C++ build system timings
generate_cxx_metadata
  [gap of 29ms]
  create-invalidation-state 113ms
  generate-prefab-packages
    exec-prefab 567ms
    [gap of 19ms]
  generate-prefab-packages completed in 595ms
  execute-generate-process
    exec-configure 783ms
    [gap of 157ms]
  execute-generate-process completed in 942ms
  [gap of 98ms]
  write-metadata-json-to-file 10ms
generate_cxx_metadata completed in 1790ms

