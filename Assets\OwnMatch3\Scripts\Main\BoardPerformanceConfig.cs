using UnityEngine;
using System;
using PrimeTween;
using OwnMatch3.Utils;

/// <summary>
/// Performance configuration for Match3Board optimizations
/// Allows fine-tuning of performance settings based on device capabilities
/// </summary>
public partial class Match3Board
{
    [Header("Performance Configuration")]
    [SerializeField] private PerformanceProfile performanceProfile = PerformanceProfile.Auto;
    [SerializeField] private bool enablePerformanceOptimizations = true;
    [SerializeField] private bool enablePerformanceMonitoring = true;
    
    // Performance monitoring
    private float _lastFrameTime;
    private float _averageFrameTime;
    private int _frameCount;
    private const int FRAME_SAMPLE_SIZE = 60;
    
    public enum PerformanceProfile
    {
        Auto,           // Automatically detect based on device
        HighEnd,        // High-end devices (60+ FPS target)
        MidRange,       // Mid-range devices (30-60 FPS target)
        LowEnd,         // Low-end devices (30 FPS target)
        Custom          // Custom settings
    }
    
    [Serializable]
    public struct PerformanceSettings
    {
        [Header("Animation Settings")]
        public int maxConcurrentAnimations;
        public float animationBatchDelay;
        public bool useAsyncJobs;
        public bool useBatchedAnimations;
        public bool useStaggeredAnimations;
        
        [Header("Job System Settings")]
        public bool enableBurstCompilation;
        public int jobBatchSize;
        public bool useJobDependencies;
        
        [Header("Memory Settings")]
        public bool usePooledNativeCollections;
        public bool preAllocateAnimationLists;
        public int initialAnimationListCapacity;
        
        [Header("Quality Settings")]
        public bool enableSmoothAnimations;
        public float fallSpeedMultiplier;
        public bool enableParticleEffects;
        
        public static PerformanceSettings GetProfileSettings(PerformanceProfile profile)
        {
            switch (profile)
            {
                case PerformanceProfile.HighEnd:
                    return new PerformanceSettings
                    {
                        maxConcurrentAnimations = 30,
                        animationBatchDelay = 0.01f,
                        useAsyncJobs = true,
                        useBatchedAnimations = true,
                        useStaggeredAnimations = true,
                        enableBurstCompilation = true,
                        jobBatchSize = 32,
                        useJobDependencies = true,
                        usePooledNativeCollections = true,
                        preAllocateAnimationLists = true,
                        initialAnimationListCapacity = 50,
                        enableSmoothAnimations = true,
                        fallSpeedMultiplier = 1.0f,
                        enableParticleEffects = true
                    };
                    
                case PerformanceProfile.MidRange:
                    return new PerformanceSettings
                    {
                        maxConcurrentAnimations = 20,
                        animationBatchDelay = 0.02f,
                        useAsyncJobs = true,
                        useBatchedAnimations = true,
                        useStaggeredAnimations = true,
                        enableBurstCompilation = true,
                        jobBatchSize = 16,
                        useJobDependencies = false,
                        usePooledNativeCollections = true,
                        preAllocateAnimationLists = true,
                        initialAnimationListCapacity = 30,
                        enableSmoothAnimations = true,
                        fallSpeedMultiplier = 1.2f,
                        enableParticleEffects = true
                    };
                    
                case PerformanceProfile.LowEnd:
                    return new PerformanceSettings
                    {
                        maxConcurrentAnimations = 10,
                        animationBatchDelay = 0.05f,
                        useAsyncJobs = false,
                        useBatchedAnimations = true,
                        useStaggeredAnimations = false,
                        enableBurstCompilation = false,
                        jobBatchSize = 8,
                        useJobDependencies = false,
                        usePooledNativeCollections = false,
                        preAllocateAnimationLists = false,
                        initialAnimationListCapacity = 10,
                        enableSmoothAnimations = false,
                        fallSpeedMultiplier = 1.5f,
                        enableParticleEffects = false
                    };
                    
                default: // Auto
                    return GetAutoDetectedSettings();
            }
        }
        
        private static PerformanceSettings GetAutoDetectedSettings()
        {
            // Auto-detect based on device capabilities
            int memorySize = SystemInfo.systemMemorySize;
            int processorCount = SystemInfo.processorCount;
            string deviceModel = SystemInfo.deviceModel.ToLower();
            
            // High-end device detection
            if (memorySize >= 6000 && processorCount >= 8)
            {
                return GetProfileSettings(PerformanceProfile.HighEnd);
            }
            // Low-end device detection
            else if (memorySize < 3000 || processorCount < 4 || 
                     deviceModel.Contains("low") || deviceModel.Contains("lite"))
            {
                return GetProfileSettings(PerformanceProfile.LowEnd);
            }
            // Default to mid-range
            else
            {
                return GetProfileSettings(PerformanceProfile.MidRange);
            }
        }
    }
    
    [SerializeField] private PerformanceSettings currentSettings;
    
    /// <summary>
    /// Initialize performance settings based on profile
    /// </summary>
    private void InitializePerformanceSettings()
    {
        currentSettings = PerformanceSettings.GetProfileSettings(performanceProfile);
        ApplyPerformanceSettings();
        
        if (enablePerformanceMonitoring)
        {
            StartPerformanceMonitoring();
        }
    }
    
    /// <summary>
    /// Apply performance settings to the board
    /// </summary>
    private void ApplyPerformanceSettings()
    {
        maxConcurrentAnimations = currentSettings.maxConcurrentAnimations;
        animationBatchDelay = currentSettings.animationBatchDelay;
        useAsyncJobs = currentSettings.useAsyncJobs;
        useBatchedAnimations = currentSettings.useBatchedAnimations;
        
        // Apply fall speed multiplier
        fallSpeed *= currentSettings.fallSpeedMultiplier;
        
        // Configure PrimeTween capacity based on settings
        if (currentSettings.enableSmoothAnimations)
        {
            PrimeTweenConfig.SetTweensCapacity(currentSettings.maxConcurrentAnimations * 4);
        }
        else
        {
            PrimeTweenConfig.SetTweensCapacity(currentSettings.maxConcurrentAnimations * 2);
        }
    }
    
    /// <summary>
    /// Start performance monitoring
    /// </summary>
    private void StartPerformanceMonitoring()
    {
        _frameCount = 0;
        _averageFrameTime = 0f;
        InvokeRepeating(nameof(MonitorPerformance), 1f, 1f);
    }
    
    /// <summary>
    /// Monitor performance and adjust settings if needed
    /// </summary>
    private void MonitorPerformance()
    {
        if (!enablePerformanceMonitoring) return;
        
        float currentFPS = 1f / Time.unscaledDeltaTime;
        _frameCount++;
        
        // Calculate rolling average
        _averageFrameTime = (_averageFrameTime * (_frameCount - 1) + Time.unscaledDeltaTime) / _frameCount;
        
        if (_frameCount >= FRAME_SAMPLE_SIZE)
        {
            float averageFPS = 1f / _averageFrameTime;
            
            // Auto-adjust settings based on performance
            if (performanceProfile == PerformanceProfile.Auto)
            {
                AdjustSettingsBasedOnPerformance(averageFPS);
            }
            
            // Reset counters
            _frameCount = 0;
            _averageFrameTime = 0f;
        }
    }
    
    /// <summary>
    /// Adjust settings based on measured performance
    /// </summary>
    private void AdjustSettingsBasedOnPerformance(float averageFPS)
    {
        if (averageFPS < 25f && currentSettings.maxConcurrentAnimations > 5)
        {
            // Performance is poor, reduce animation load
            currentSettings.maxConcurrentAnimations = Mathf.Max(5, currentSettings.maxConcurrentAnimations - 5);
            currentSettings.animationBatchDelay += 0.01f;
            ApplyPerformanceSettings();
            
            DebugManager.LogMatch($"[Performance] Reduced animation load due to low FPS: {averageFPS:F1}");
        }
        else if (averageFPS > 55f && currentSettings.maxConcurrentAnimations < 30)
        {
            // Performance is good, can increase quality
            currentSettings.maxConcurrentAnimations = Mathf.Min(30, currentSettings.maxConcurrentAnimations + 2);
            currentSettings.animationBatchDelay = Mathf.Max(0.01f, currentSettings.animationBatchDelay - 0.005f);
            ApplyPerformanceSettings();
            
            DebugManager.LogMatch($"[Performance] Increased animation quality due to good FPS: {averageFPS:F1}");
        }
    }
    
    /// <summary>
    /// Get current performance statistics
    /// </summary>
    public (float fps, int animationCount, string profile) GetPerformanceStats()
    {
        float currentFPS = 1f / Time.unscaledDeltaTime;
        return (currentFPS, currentSettings.maxConcurrentAnimations, performanceProfile.ToString());
    }
    
    /// <summary>
    /// Manually set performance profile
    /// </summary>
    public void SetPerformanceProfile(PerformanceProfile profile)
    {
        performanceProfile = profile;
        InitializePerformanceSettings();
    }
    
    /// <summary>
    /// Enable or disable performance optimizations
    /// </summary>
    public void SetPerformanceOptimizations(bool enabled)
    {
        enablePerformanceOptimizations = enabled;
        if (enabled)
        {
            InitializePerformanceSettings();
        }
    }
}
