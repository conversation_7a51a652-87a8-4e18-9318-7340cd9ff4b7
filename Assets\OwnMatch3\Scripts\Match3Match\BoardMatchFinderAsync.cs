using Unity.Collections;
using Unity.Jobs;
using UnityEngine;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using OwnMatch3.Utils;

/// <summary>
/// Async match finder optimization for Match3Board
/// Specifically addresses the synchronous job completion bottleneck
/// </summary>
public partial class Match3Board
{
    /// <summary>
    /// Async version of DetectAllMatches that prevents frame blocking
    /// </summary>
    public async UniTask<List<MatchInfo>> DetectAllMatchesAsync(bool checkBonusShapes = true)
    {
        if (!enablePerformanceOptimizations || !useAsyncJobs)
        {
            // Fallback to original method
            return DetectAllMatches(checkBonusShapes);
        }
        
        return await _matchFinder.DetectMatchesInLayoutAsync(gems);
    }
}

/// <summary>
/// Extension to MatchFinder for async job completion
/// </summary>
public static class MatchFinderAsyncExtensions
{
    /// <summary>
    /// Async version of DetectMatchesInLayout that prevents blocking
    /// </summary>
    public static async UniTask<List<MatchInfo>> DetectMatchesInLayoutAsync(this MatchFinder matchFinder, Dictionary<Vector3Int, GemNew> gems)
    {
        int gemCount = gems.Count;
        if (gemCount == 0) return new List<MatchInfo>();
        
        // Get the private fields using reflection (temporary solution)
        var gemPositionsField = typeof(MatchFinder).GetField("gemPositions", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var gemTypesField = typeof(MatchFinder).GetField("gemTypes", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var gemGridField = typeof(MatchFinder).GetField("gemGrid", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var matchesField = typeof(MatchFinder).GetField("matches", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var matchedPositionsField = typeof(MatchFinder).GetField("matchedPositions", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var shapesField = typeof(MatchFinder).GetField("shapes", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var shapeCellsField = typeof(MatchFinder).GetField("shapeCells", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var boardField = typeof(MatchFinder).GetField("board", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        if (gemPositionsField == null || gemTypesField == null || gemGridField == null || 
            matchesField == null || matchedPositionsField == null || shapesField == null || 
            shapeCellsField == null || boardField == null)
        {
            // Fallback to original method if reflection fails
            return matchFinder.DetectMatchesInLayout(gems);
        }
        
        var gemPositions = (NativeArray<Vector3Int>)gemPositionsField.GetValue(matchFinder);
        var gemTypes = (NativeArray<int>)gemTypesField.GetValue(matchFinder);
        var gemGrid = (NativeHashMap<Vector3Int, int>)gemGridField.GetValue(matchFinder);
        var matches = (NativeList<MatchFinder.MatchInfoJobData>)matchesField.GetValue(matchFinder);
        var matchedPositions = (NativeList<Vector3Int>)matchedPositionsField.GetValue(matchFinder);
        var shapes = (NativeArray<MatchFinder.ShapeJobData>)shapesField.GetValue(matchFinder);
        var shapeCells = (NativeArray<Vector3Int>)shapeCellsField.GetValue(matchFinder);
        var board = (Match3Board)boardField.GetValue(matchFinder);
        
        if (!shapes.IsCreated) return new List<MatchInfo>();
        
        // Resize arrays if needed
        if (gemPositions.Length < gemCount)
        {
            // Call the resize method using reflection
            var resizeMethod = typeof(MatchFinder).GetMethod("ResizeArrays", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (resizeMethod != null)
            {
                resizeMethod.Invoke(matchFinder, new object[] { gemCount });
                
                // Re-get the resized arrays
                gemPositions = (NativeArray<Vector3Int>)gemPositionsField.GetValue(matchFinder);
                gemTypes = (NativeArray<int>)gemTypesField.GetValue(matchFinder);
                gemGrid = (NativeHashMap<Vector3Int, int>)gemGridField.GetValue(matchFinder);
            }
        }
        
        gemGrid.Clear();
        matches.Clear();
        matchedPositions.Clear();
        
        int i = 0;
        int skippedGems = 0;
        foreach (var kvp in gems)
        {
            // CRITICAL FIX: Ice gems should be included in match detection
            // Only IceAdjacentBreakable gems should be excluded from matching
            if (board.IsGemUnmatchableByIce(kvp.Key))
            {
                skippedGems++;
                DebugManager.LogMatch($"🔍 MATCH FINDER: Skipping unmatchable gem {kvp.Value.GemType} at {kvp.Key} (IceAdjacentBreakable)");
                continue; // Skip gems behind IceAdjacentBreakable obstacles
            }

            // Include Ice gems in match detection - they can participate in matches
            gemPositions[i] = kvp.Key;
            gemTypes[i] = (int)kvp.Value.GemType;
            gemGrid.Add(kvp.Key, (int)kvp.Value.GemType);
            i++;
        }

        DebugManager.LogMatch($"🔍 MATCH FINDER: Processing {i} gems, skipped {skippedGems} unmatchable gems");
        DebugManager.LogMatch($"🔍 MATCH FINDER: Using {shapes.Length} shape patterns for detection");

        var job = new Match3Board.DetectMatchesJob
        {
            GemPositions = gemPositions.GetSubArray(0, i),
            GemTypes = gemTypes.GetSubArray(0, i),
            GemGrid = gemGrid,
            Shapes = shapes,
            ShapeCells = shapeCells,
            Matches = matches,
            MatchedPositions = matchedPositions
        };

        JobHandle handle = job.Schedule();
        
        // PERFORMANCE FIX: Wait for job completion asynchronously instead of blocking
        while (!handle.IsCompleted)
        {
            await UniTask.Yield();
        }
        
        handle.Complete();

        // Call ProcessJobResults using reflection
        var processMethod = typeof(MatchFinder).GetMethod("ProcessJobResults", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (processMethod != null)
        {
            return (List<MatchInfo>)processMethod.Invoke(matchFinder, null);
        }
        
        // Fallback to original method if reflection fails
        return matchFinder.DetectMatchesInLayout(gems);
    }
}

/// <summary>
/// Simple async wrapper for match detection
/// </summary>
public static class AsyncMatchDetection
{
    /// <summary>
    /// Create an async wrapper around the existing match detection
    /// </summary>
    public static async UniTask<List<MatchInfo>> DetectMatchesAsync(Match3Board board)
    {
        // Yield control to prevent blocking
        await UniTask.Yield();
        
        // Use the existing match detection
        return board.DetectAllMatches();
    }
    
    /// <summary>
    /// Detect matches with frame spreading for better performance
    /// </summary>
    public static async UniTask<List<MatchInfo>> DetectMatchesWithFrameSpreadingAsync(Match3Board board, int maxGemsPerFrame = 50)
    {
        var allMatches = new List<MatchInfo>();
        var gemList = new List<KeyValuePair<Vector3Int, GemNew>>(board.gems);
        
        // Process gems in batches to spread work across frames
        for (int i = 0; i < gemList.Count; i += maxGemsPerFrame)
        {
            // Yield control every batch
            if (i > 0)
            {
                await UniTask.Yield();
            }
            
            // Process this batch (simplified - you'd need to implement partial match detection)
            // For now, just yield and continue
        }
        
        // For now, fall back to the original method after frame spreading
        await UniTask.Yield();
        return board.DetectAllMatches();
    }
    
    /// <summary>
    /// Check if performance optimizations should be used
    /// </summary>
    public static bool ShouldUseOptimizations(Match3Board board)
    {
        // Access public properties instead of private fields
        return board.GetPerformanceOptimizationsEnabled() && board.GetAsyncJobsEnabled();
    }
}
