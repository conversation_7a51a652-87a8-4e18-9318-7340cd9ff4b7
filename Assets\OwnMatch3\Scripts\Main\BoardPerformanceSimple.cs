using PrimeTween;
using UnityEngine;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using OwnMatch3.Utils;
using System;

/// <summary>
/// Simple performance optimizations for Match3Board
/// Focuses on the main bottlenecks without complex systems
/// </summary>
public partial class Match3Board
{
    [Header("Simple Performance Settings")]
    [SerializeField] private bool useSimpleOptimizations = true;
    [SerializeField] private int maxDestructionBatchSize = 15;
    [SerializeField] private float destructionBatchDelay = 0.03f;
    [SerializeField] private bool cacheUIReferences = true;
    
    // Cached UI references to avoid FindFirstObjectByType calls
    private GameUI _cachedGameUISimple;
    private GameUIDocument _cachedGameUIDocumentSimple;
    private bool _uiReferencesCached = false;
    
    /// <summary>
    /// Simple optimized version of ClearMatchesAsync
    /// Addresses the main performance bottlenecks
    /// </summary>
    public async UniTask<bool> ClearMatchesAsyncSimple()
    {
        if (!useSimpleOptimizations)
        {
            return await ClearMatchesAsync();
        }
        
        // Hide any active hints since matches are being cleared
        OnPlayerActivity();
        
        try
        {
            // Yield control before heavy operations
            await UniTask.Yield();
            
            var matches = DetectAllMatches();
            
            if (matches.Count == 0)
            {
                // No matches – combo chain ends; reset counters and sound pitch
                ComboChainLevel = 0;
                CascadeIndex = 0;
                ComboManager.Instance?.ResetComboChain();
                GemSoundSystem.Instance?.ResetPitch();
                return false;
            }
            
            // COMBO FIX: Only increment combo level for cascading matches, not the initial player match
            if (!IsFirstMatchBatch)
            {
                ComboChainLevel++;
                DebugManager.LogMatch($"[BoardPerformanceSimple] CASCADING MATCH - Combo level incremented to: {ComboChainLevel}");
            }
            else
            {
                DebugManager.LogMatch($"[BoardPerformanceSimple] INITIAL PLAYER MATCH - No combo bonus (ComboLevel stays: {ComboChainLevel})");
            }
            
            // Update ScoreManager with current combo level
            if (ScoreManager.Instance != null)
            {
                ScoreManager.Instance.SetComboLevel(ComboChainLevel);
            }
            
            CascadeIndex++;
            
            // Show combo text only when cascade index >=4 (after 3 cascades)
            if (CascadeIndex >= 4)
            {
                ComboManager.Instance?.ShowCombo(CascadeIndex);
            }
            
            // Clear first-batch flag after it is processed
            if (IsFirstMatchBatch) IsFirstMatchBatch = false;
            
            // Process matches with simple batching
            await ProcessMatchesSimple(matches);
            
            return true;
        }
        catch (Exception ex)
        {
            DebugManager.LogMatchError($"[BoardPerformanceSimple] Error in ClearMatchesAsyncSimple: {ex.Message}");
            // Fallback to original method
            return await ClearMatchesAsync();
        }
    }
    
    /// <summary>
    /// Simple match processing with basic batching
    /// </summary>
    private async UniTask ProcessMatchesSimple(List<MatchInfo> matches)
    {
        var pendingBonuses = matches.Where(m => m.Type == MatchType.BonusShape && m.bonusGemData != null).ToList();
        
        // Collect unique positions and gems to destroy
        var uniquePositions = new HashSet<Vector3Int>();
        var gemsToDestroy = new List<GemNew>();
        var destroyedPositions = new List<Vector3Int>();
        var matchedGemTypes = new List<GemType>();
        
        foreach (var match in matches)
        {
            foreach (var pos in match.positions)
            {
                if (uniquePositions.Add(pos) && gems.TryGetValue(pos, out var gem))
                {
                    gemsToDestroy.Add(gem);
                }
            }
        }
        
        // Process gem destruction in simple batches
        await ProcessGemDestructionSimple(gemsToDestroy, destroyedPositions, matchedGemTypes);
        
        // Handle bonus gem creation (simplified)
        if (pendingBonuses.Count > 0)
        {
            await ProcessBonusGemsSimple(pendingBonuses);
        }
        
        // Update UI efficiently
        UpdateUISimple(destroyedPositions, matchedGemTypes);
        
        // Clean up gem positions
        CleanupGemsSimple(uniquePositions);
    }
    
    /// <summary>
    /// Simple gem destruction with basic batching
    /// </summary>
    private async UniTask ProcessGemDestructionSimple(List<GemNew> gemsToDestroy, 
        List<Vector3Int> destroyedPositions, List<GemType> matchedGemTypes)
    {
        var destructionTasks = new List<UniTask>();
        int processedCount = 0;
        
        foreach (var gem in gemsToDestroy)
        {
            // Skip hidden gems behind ice
            if (IsGemHiddenByIce(gem.CurrentCell))
            {
                continue;
            }
            
            destroyedPositions.Add(gem.CurrentCell);
            matchedGemTypes.Add(gem.GemType);
            
            // Handle obstacles
            HandleObstacleAt(gem.CurrentCell);
            HandleAdjacentObstacles(new[] { gem.CurrentCell });
            
            // Add destruction task
            destructionTasks.Add(gem.OnMatched(this));
            processedCount++;
            
            // Process batch when it reaches the limit
            if (destructionTasks.Count >= maxDestructionBatchSize)
            {
                await UniTask.WhenAll(destructionTasks);
                destructionTasks.Clear();
                
                // Small delay between batches to spread load
                if (processedCount < gemsToDestroy.Count)
                {
                    await UniTask.Delay(TimeSpan.FromSeconds(destructionBatchDelay));
                }
            }
        }
        
        // Process remaining tasks
        if (destructionTasks.Count > 0)
        {
            await UniTask.WhenAll(destructionTasks);
        }
    }
    
    /// <summary>
    /// Simple bonus gem processing
    /// </summary>
    private async UniTask ProcessBonusGemsSimple(List<MatchInfo> pendingBonuses)
    {
        var bonusTasks = new List<UniTask>();
        
        foreach (var bonusMatch in pendingBonuses)
        {
            if (bonusMatch.bonusGemData != null && bonusMatch.positions.Count > 0)
            {
                var spawnPos = bonusMatch.positions[0];
                // Add bonus creation logic here if needed
                bonusTasks.Add(UniTask.Yield()); // Placeholder
                
                // Limit concurrent bonus creation
                if (bonusTasks.Count >= 3)
                {
                    await UniTask.WhenAll(bonusTasks);
                    bonusTasks.Clear();
                }
            }
        }
        
        if (bonusTasks.Count > 0)
        {
            await UniTask.WhenAll(bonusTasks);
        }
    }
    
    /// <summary>
    /// Efficient UI updates with caching
    /// </summary>
    private void UpdateUISimple(List<Vector3Int> destroyedPositions, List<GemType> matchedGemTypes)
    {
        // Cache UI references to avoid expensive FindFirstObjectByType calls
        if (cacheUIReferences && !_uiReferencesCached)
        {
            _cachedGameUISimple = FindFirstObjectByType<GameUI>();
            _cachedGameUIDocumentSimple = FindFirstObjectByType<GameUIDocument>();
            _uiReferencesCached = true;
        }
        
        // Trigger match completion event
        OnMatchCompleted(destroyedPositions, matchedGemTypes);
        
        // Update UI efficiently
        if (cacheUIReferences)
        {
            _cachedGameUISimple?.UpdateGoalsDisplay();
            _cachedGameUIDocumentSimple?.RefreshUI();
        }
        else
        {
            // Fallback to original method
            var gameUI = FindFirstObjectByType<GameUI>();
            gameUI?.UpdateGoalsDisplay();
            var uiDoc = FindFirstObjectByType<GameUIDocument>();
            uiDoc?.RefreshUI();
        }
    }
    
    /// <summary>
    /// Simple gem cleanup
    /// </summary>
    private void CleanupGemsSimple(HashSet<Vector3Int> uniquePositions)
    {
        foreach (var pos in uniquePositions)
        {
            if (gems.ContainsKey(pos))
            {
                // Don't remove gems that are still protected by ice obstacles
                if (IsGemHiddenByIce(pos))
                {
                    continue;
                }
                
                gems.Remove(pos);
                MarkCellForRefill(pos);
            }
        }
    }
    
    /// <summary>
    /// Enable or disable simple optimizations
    /// </summary>
    public void SetSimpleOptimizations(bool enabled)
    {
        useSimpleOptimizations = enabled;
        
        if (enabled)
        {
            DebugManager.LogMatch("[BoardPerformanceSimple] Simple optimizations enabled");
        }
        else
        {
            DebugManager.LogMatch("[BoardPerformanceSimple] Simple optimizations disabled");
        }
    }
    
    /// <summary>
    /// Get simple optimization settings
    /// </summary>
    public (bool enabled, int batchSize, float delay) GetSimpleOptimizationSettings()
    {
        return (useSimpleOptimizations, maxDestructionBatchSize, destructionBatchDelay);
    }
}
