apply plugin: 'com.android.library'
apply from: '../shared/keepUnitySymbols.gradle'
apply from: '../shared/common.gradle'


dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
// Android Resolver Dependencies Start
    implementation 'com.google.android.gms:play-services-ads-identifier:18.1.0' // Assets/LevelPlay/Editor/IronSourceSDKDependencies.xml:12
    implementation 'com.google.android.play:app-update:2.1.0' // Assets/GooglePlayPlugins/com.google.play.appupdate/Editor/Dependencies.xml:3
    implementation 'com.google.android.play:core-common:2.0.4' // Assets/GooglePlayPlugins/com.google.play.core/Editor/Dependencies.xml:3
    implementation 'com.google.games:gpgs-plugin-support:2.0.0' // Assets/GooglePlayGames/com.google.play.games/Editor/GooglePlayGamesPluginDependencies.xml:11
    implementation 'com.unity3d.ads:unity-ads:4.15.1' // Assets/LevelPlay/Editor/ISUnityAdsAdapterDependencies.xml:12
    implementation 'com.unity3d.ads-mediation:mediation-sdk:8.10.0' // Assets/LevelPlay/Editor/IronSourceSDKDependencies.xml:5
    implementation 'com.unity3d.ads-mediation:unityads-adapter:4.3.57' // Assets/LevelPlay/Editor/ISUnityAdsAdapterDependencies.xml:5
// Android Resolver Dependencies End
    implementation project(':unityLibrary:IronSource.androidlib')
    implementation project(':unityLibrary:GooglePlayGamesManifest.androidlib')
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.core:core:1.9.0'
    implementation 'androidx.games:games-activity:3.0.5'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'

	implementation 'com.google.android.play:asset-delivery:2.1.0'
}

// Android Resolver Exclusions Start
android {
  packaging {
      exclude ('/lib/armeabi/*' + '*')
      exclude ('/lib/armeabi-v7a/*' + '*')
      exclude ('/lib/mips/*' + '*')
      exclude ('/lib/mips64/*' + '*')
      exclude ('/lib/x86/*' + '*')
      exclude ('/lib/x86_64/*' + '*')
  }
}
// Android Resolver Exclusions End
android {
    namespace "com.unity3d.player"
    ndkPath "F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/NDK"
    ndkVersion "27.2.12479018"

    compileSdk 36
    buildToolsVersion = "34.0.0"

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    defaultConfig {
        minSdk 23
        targetSdk 36
        ndk {
            abiFilters 'arm64-v8a'
            debugSymbolLevel 'none'
        }
        versionCode 13
        versionName '0.2.01'
        consumerProguardFiles 'proguard-unity.txt', 'proguard-user.txt'
        externalNativeBuild {
            cmake {
                arguments "-DANDROID_STL=c++_shared", "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON"
            }
        }

    }

    lint {
        abortOnError false
    }

    androidResources {
        noCompress = ['.unity3d', '.ress', '.resource', '.obb', '.bundle', '.unityexp'] + unityStreamingAssets.tokenize(', ')
        ignoreAssetsPattern = "!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"
    }

    packaging {
        jniLibs {
                useLegacyPackaging true
        }
    }
}


android.externalNativeBuild {
    cmake {
        version "3.22.1"
        // Workaround for CMake long path issue on Windows, place CMake intermediate files next to Unity project
        buildStagingDirectory "${unityProjectPath}/.utmp"
        path "src/main/cpp/CMakeLists.txt"
    }
}
android.buildFeatures {
    prefab true
}

