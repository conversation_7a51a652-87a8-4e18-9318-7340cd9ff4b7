%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &1
MonoBehaviour:
  m_ObjectHideFlags: 53
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0355ba069dae4d24bf2e318d15442628, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.AI.Image::Unity.AI.Image.Services.SessionPersistence.TextureGeneratorSettings
  m_Session:
    settings:
      lastSelectedModels:
        serializedData:
        - key: 0
          value:
            rid: 6297302814581588119
        - key: 1
          value:
            rid: 6297302814581588120
        - key: 2
          value:
            rid: 6297302814581588121
        - key: 3
          value:
            rid: 6297302814581588122
        - key: 4
          value:
            rid: 6297302814581588123
        - key: 5
          value:
            rid: 6297302814581588124
      previewSettings:
        sizeFactor: 1
  references:
    version: 2
    RefIds:
    - rid: 6297302814581588119
      type: {class: ModelSelection, ns: Unity.AI.Image.Services.Stores.States, asm: Unity.AI.Image}
      data:
        modelID: 81c7bc50-f1fa-4e43-96be-2baa3f6233b6
    - rid: 6297302814581588120
      type: {class: ModelSelection, ns: Unity.AI.Image.Services.Stores.States, asm: Unity.AI.Image}
      data:
        modelID: 
    - rid: 6297302814581588121
      type: {class: ModelSelection, ns: Unity.AI.Image.Services.Stores.States, asm: Unity.AI.Image}
      data:
        modelID: 
    - rid: 6297302814581588122
      type: {class: ModelSelection, ns: Unity.AI.Image.Services.Stores.States, asm: Unity.AI.Image}
      data:
        modelID: 2d2d53fc-e209-4853-a358-738191781d9c
    - rid: 6297302814581588123
      type: {class: ModelSelection, ns: Unity.AI.Image.Services.Stores.States, asm: Unity.AI.Image}
      data:
        modelID: 2d2d53fc-e209-4853-a358-738191781d9c
    - rid: 6297302814581588124
      type: {class: ModelSelection, ns: Unity.AI.Image.Services.Stores.States, asm: Unity.AI.Image}
      data:
        modelID: 55f71215-6b73-4707-881e-69c61aca68c0
