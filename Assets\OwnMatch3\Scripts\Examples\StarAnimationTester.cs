using UnityEngine;
using UnityEngine.UIElements;
using PhantomExtra;

using OwnMatch3.Utils;
/// <summary>
/// Test script for the enhanced star rating animations.
/// Provides easy testing of the spectacular star effects.
/// </summary>
public class StarAnimationTester : MonoBehaviour
{
    [Header("UI References")]
    [SerializeField] private UIDocument uiDocument;
    [SerializeField] private string starContainerName = "StarContainers";
    
    [Header("Test Controls")]
    [SerializeField] private KeyCode test1StarKey = KeyCode.Alpha1;
    [SerializeField] private KeyCode test2StarsKey = KeyCode.Alpha2;
    [SerializeField] private KeyCode test3StarsKey = KeyCode.Alpha3;
    [SerializeField] private KeyCode resetStarsKey = KeyCode.R;
    
    [Header("Animation Settings")]
    [SerializeField] private bool enableAnim = true;
    [SerializeField] private float animationDelay = 0.3f;
    [SerializeField] private float animationDuration = 0.8f;
    [SerializeField] private float maxScale = 1.5f;
    [SerializeField] private float rotationAmount = 720f;
    [SerializeField] private bool enableCameraShake = true;
    [SerializeField] private float shakeIntensity = 1f;
    
    private StarRatingElement starContainer;
    
    void Start()
    {
        if (uiDocument == null)
        {
            uiDocument = FindFirstObjectByType<UIDocument>();
        }
        
        InitializeStarContainer();
    }
    
    void Update()
    {
        if (starContainer == null) return;
        
        if (Input.GetKeyDown(test1StarKey))
        {
            TestStarAnimation(1);
        }
        else if (Input.GetKeyDown(test2StarsKey))
        {
            TestStarAnimation(2);
        }
        else if (Input.GetKeyDown(test3StarsKey))
        {
            TestStarAnimation(3);
        }
        else if (Input.GetKeyDown(resetStarsKey))
        {
            ResetStars();
        }
    }
    
    /// <summary>
    /// Initialize the star container with enhanced animation settings
    /// </summary>
    private void InitializeStarContainer()
    {
        if (uiDocument == null)
        {
            DebugManager.LogTestingError("[StarAnimationTester] UIDocument not found!");
            return;
        }
        
        var root = uiDocument.rootVisualElement;
        starContainer = root.Q<StarRatingElement>(starContainerName);
        
        if (starContainer == null)
        {
            DebugManager.LogTestingError($"[StarAnimationTester] StarRatingElement '{starContainerName}' not found!");
            return;
        }
        
        // Configure animation settings
        starContainer.EnableAnimations = enableAnim;
        starContainer.AnimationDelay = animationDelay;
        starContainer.AnimationDuration = animationDuration;
        starContainer.MaxScale = maxScale;
        starContainer.RotationAmount = rotationAmount;
        starContainer.EnableCameraShake = enableCameraShake;
        starContainer.ShakeIntensity = shakeIntensity;
        
        DebugManager.LogTesting("[StarAnimationTester] Star container initialized with enhanced animations");
    }
    
    /// <summary>
    /// Test star animation with specified number of stars
    /// </summary>
    [ContextMenu("Test 1 Star")]
    public void Test1Star() => TestStarAnimation(1);
    
    [ContextMenu("Test 2 Stars")]
    public void Test2Stars() => TestStarAnimation(2);
    
    [ContextMenu("Test 3 Stars")]
    public void Test3Stars() => TestStarAnimation(3);
    
    [ContextMenu("Reset Stars")]
    public void ResetStars()
    {
        if (starContainer != null)
        {
            starContainer.ResetStars();
            DebugManager.LogTesting("[StarAnimationTester] Stars reset");
        }
    }
    
    /// <summary>
    /// Test star animation with specified number of stars
    /// </summary>
    public void TestStarAnimation(int stars)
    {
        if (starContainer == null)
        {
            DebugManager.LogTestingError("[StarAnimationTester] Star container not initialized!");
            return;
        }
        
        stars = Mathf.Clamp(stars, 0, 3);
        DebugManager.LogTesting($"[StarAnimationTester] Testing {stars} star animation");
        
        // Reset first, then animate
        starContainer.ResetStars();
        
        // Small delay to ensure reset is complete
        Invoke(nameof(TriggerAnimation), 0.1f);
        
        void TriggerAnimation()
        {
            starContainer.PlayStarAnimation(stars);
        }
    }
    
    /// <summary>
    /// Update animation settings at runtime
    /// </summary>
    [ContextMenu("Update Animation Settings")]
    public void UpdateAnimationSettings()
    {
        if (starContainer != null)
        {
            starContainer.EnableAnimations = enableAnim;
            starContainer.AnimationDelay = animationDelay;
            starContainer.AnimationDuration = animationDuration;
            starContainer.MaxScale = maxScale;
            starContainer.RotationAmount = rotationAmount;
            starContainer.EnableCameraShake = enableCameraShake;
            starContainer.ShakeIntensity = shakeIntensity;
            
            DebugManager.LogTesting("[StarAnimationTester] Animation settings updated");
        }
    }
    
    void OnGUI()
    {
        // Simple GUI for testing
        GUILayout.BeginArea(new Rect(10, 10, 300, 250));
        GUILayout.Label("Star Animation Tester");
        GUILayout.Label($"Press '{test1StarKey}' for 1 star");
        GUILayout.Label($"Press '{test2StarsKey}' for 2 stars");
        GUILayout.Label($"Press '{test3StarsKey}' for 3 stars");
        GUILayout.Label($"Press '{resetStarsKey}' to reset");
        
        GUILayout.Space(10);
        
        if (GUILayout.Button("Test 1 Star"))
        {
            TestStarAnimation(1);
        }
        
        if (GUILayout.Button("Test 2 Stars"))
        {
            TestStarAnimation(2);
        }
        
        if (GUILayout.Button("Test 3 Stars"))
        {
            TestStarAnimation(3);
        }
        
        if (GUILayout.Button("Reset Stars"))
        {
            ResetStars();
        }
        
        GUILayout.Space(10);
        
        GUILayout.Label("Animation Settings:");
        enableAnim = GUILayout.Toggle(enableAnim, "Enable Animations");
        enableCameraShake = GUILayout.Toggle(enableCameraShake, "Enable Camera Shake");
        
        GUILayout.Label($"Animation Delay: {animationDelay:F1}s");
        animationDelay = GUILayout.HorizontalSlider(animationDelay, 0.1f, 1f);
        
        GUILayout.Label($"Max Scale: {maxScale:F1}x");
        maxScale = GUILayout.HorizontalSlider(maxScale, 1.1f, 2f);
        
        GUILayout.Label($"Rotation: {rotationAmount:F0}°");
        rotationAmount = GUILayout.HorizontalSlider(rotationAmount, 180f, 1080f);
        
        if (GUILayout.Button("Update Settings"))
        {
            UpdateAnimationSettings();
        }
        
        GUILayout.EndArea();
    }
}
