{ "pid": 18488, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 18488, "tid": 1, "ts": 1753478344470101, "dur": 41020, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 18488, "tid": 1, "ts": 1753478344511127, "dur": 687151, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 18488, "tid": 1, "ts": 1753478345198286, "dur": 11348, "ph": "X", "name": "Write<PERSON>son", "args": {} },
{ "pid": 18488, "tid": 1240, "ts": 1753478352698830, "dur": 1165, "ph": "X", "name": "", "args": {} },
{ "pid": 18488, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 18488, "tid": 12884901888, "ts": 1753478344468571, "dur": 32081, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 18488, "tid": 12884901888, "ts": 1753478344500656, "dur": 8185083, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 18488, "tid": 12884901888, "ts": 1753478344501544, "dur": 2455, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 18488, "tid": 12884901888, "ts": 1753478344504010, "dur": 547, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 18488, "tid": 12884901888, "ts": 1753478344504562, "dur": 7428, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 18488, "tid": 12884901888, "ts": 1753478344512000, "dur": 162, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 18488, "tid": 12884901888, "ts": 1753478344512165, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 18488, "tid": 12884901888, "ts": 1753478344512233, "dur": 727, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 18488, "tid": 12884901888, "ts": 1753478344512965, "dur": 23104, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 18488, "tid": 12884901888, "ts": 1753478344536080, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 18488, "tid": 12884901888, "ts": 1753478344536084, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 18488, "tid": 12884901888, "ts": 1753478344536142, "dur": 870, "ph": "X", "name": "ProcessMessages 741", "args": {} },
{ "pid": 18488, "tid": 12884901888, "ts": 1753478344537017, "dur": 53, "ph": "X", "name": "ReadAsync 741", "args": {} },
{ "pid": 18488, "tid": 12884901888, "ts": 1753478344537075, "dur": 556, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 18488, "tid": 12884901888, "ts": 1753478344537635, "dur": 127014, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 18488, "tid": 12884901888, "ts": 1753478344664661, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 18488, "tid": 12884901888, "ts": 1753478344664666, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 18488, "tid": 12884901888, "ts": 1753478344664717, "dur": 639, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 18488, "tid": 12884901888, "ts": 1753478344665365, "dur": 8004490, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 18488, "tid": 12884901888, "ts": 1753478352669865, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 18488, "tid": 12884901888, "ts": 1753478352669869, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 18488, "tid": 12884901888, "ts": 1753478352669918, "dur": 1679, "ph": "X", "name": "ProcessMessages 264", "args": {} },
{ "pid": 18488, "tid": 12884901888, "ts": 1753478352671601, "dur": 45, "ph": "X", "name": "ReadAsync 264", "args": {} },
{ "pid": 18488, "tid": 12884901888, "ts": 1753478352671649, "dur": 195, "ph": "X", "name": "ProcessMessages 41", "args": {} },
{ "pid": 18488, "tid": 12884901888, "ts": 1753478352671846, "dur": 13131, "ph": "X", "name": "ReadAsync 41", "args": {} },
{ "pid": 18488, "tid": 1240, "ts": 1753478352700002, "dur": 27, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 18488, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 18488, "tid": 8589934592, "ts": 1753478344466175, "dur": 743500, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 18488, "tid": 8589934592, "ts": 1753478345209678, "dur": 20, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 18488, "tid": 8589934592, "ts": 1753478345209700, "dur": 1325, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 18488, "tid": 1240, "ts": 1753478352700030, "dur": 16, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 18488, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 18488, "tid": 4294967296, "ts": 1753478344396583, "dur": 8290346, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 18488, "tid": 4294967296, "ts": 1753478344402753, "dur": 57460, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 18488, "tid": 4294967296, "ts": 1753478352687001, "dur": 5167, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 18488, "tid": 4294967296, "ts": 1753478352690321, "dur": 38, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 18488, "tid": 4294967296, "ts": 1753478352692275, "dur": 18, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 18488, "tid": 1240, "ts": 1753478352700048, "dur": 93, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1753478344498788, "dur":33314, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753478344532116, "dur":3485, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753478344535620, "dur":98, "ph":"X", "name": "Tundra",  "args": { "detail":"PrepareNodes" }}
,{ "pid":12345, "tid":0, "ts":1753478344535718, "dur":279, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753478344536017, "dur":81, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753478344536098, "dur":8134503, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753478352670697, "dur":8088, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1753478344536351, "dur":401, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1753478344537407, "dur":126671, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\OwnMatch3.dll" }}
,{ "pid":12345, "tid":1, "ts":1753478344536757, "dur":127489, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":1, "ts":1753478344665481, "dur":8004363, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":2, "ts":1753478344536443, "dur":720, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753478344537164, "dur":8133389, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753478344536381, "dur":714, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753478344537096, "dur":8133478, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753478344536376, "dur":935, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753478344537312, "dur":8133225, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753478344536430, "dur":544, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753478344536974, "dur":8133615, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1753478344536508, "dur":873, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1753478344537382, "dur":8133128, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1753478344536530, "dur":674486, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1753478345212655, "dur":181, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"F:/Unity Installs/6000.2.0b9/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":7, "ts":1753478345211017, "dur":1829, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1753478345212846, "dur":7457657, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1753478344536564, "dur":676287, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1753478345212852, "dur":7457609, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753478352684389, "dur":259, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 18488, "tid": 1240, "ts": 1753478352700651, "dur": 1675, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 18488, "tid": 1240, "ts": 1753478352702370, "dur": 443, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 18488, "tid": 1240, "ts": 1753478352698108, "dur": 5420, "ph": "X", "name": "Write chrome-trace events", "args": {} },
