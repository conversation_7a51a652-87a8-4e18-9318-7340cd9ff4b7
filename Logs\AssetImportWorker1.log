Using pre-set license
Built from '6000.2/staging' branch; Version is '6000.2.0b9 (377f5a9787ef) revision 3637082'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 32596 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-07-26T11:15:04Z

COMMAND LINE ARGUMENTS:
F:\Unity Installs\6000.2.0b9\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
F:/Match2D
-logFile
Logs/AssetImportWorker1.log
-srvPort
1867
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: F:/Match2D
F:/Match2D
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [18880]  Target information:

Player connection [18880]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 3086110228 [EditorId] 3086110228 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-8GO5TD1) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [18880] Host joined multi-casting on [***********:54997]...
Player connection [18880] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 17.23 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 1.78 ms.
Initialize engine version: 6000.2.0b9 (377f5a9787ef)
[Subsystems] Discovering subsystems at path F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/Match2D/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1660 SUPER (ID=0x21c4)
    Vendor:   NVIDIA
    VRAM:     5966 MB
    Driver:   32.0.15.7657
Initialize mono
Mono path[0] = 'F:/Unity Installs/6000.2.0b9/Editor/Data/Managed'
Mono path[1] = 'F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56128
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.001097 seconds.
- Loaded All Assemblies, in  0.422 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 197 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.632 seconds
Domain Reload Profiling: 1053ms
	BeginReloadAssembly (130ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (201ms)
		LoadAssemblies (129ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (197ms)
			TypeCache.Refresh (196ms)
				TypeCache.ScanAssembly (183ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (632ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (562ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (332ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (59ms)
			ProcessInitializeOnLoadAttributes (121ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.120 seconds
Refreshing native plugins compatible for Editor in 5.37 ms, found 9 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
GPGSUpgrader start
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:38)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 38)

GPGSUpgrader done
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:57)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 57)

Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <e887908fdf9b4028bc879f0c1572eba4>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Refreshing native plugins compatible for Editor in 8.19 ms, found 9 plugins.
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.226 seconds
Domain Reload Profiling: 2345ms
	BeginReloadAssembly (232ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (39ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (807ms)
		LoadAssemblies (476ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (444ms)
			TypeCache.Refresh (318ms)
				TypeCache.ScanAssembly (291ms)
			BuildScriptInfoCaches (109ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1226ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1039ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (203ms)
			ProcessInitializeOnLoadAttributes (530ms)
			ProcessInitializeOnLoadMethodAttributes (285ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 10.64 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.08 ms.
Unloading 70 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10474 unused Assets / (22.4 MB). Loaded Objects now: 13182.
Memory consumption went from 287.5 MB to 265.0 MB.
Total: 22.062900 ms (FindLiveObjects: 1.315800 ms CreateObjectMapping: 1.693500 ms MarkObjects: 9.369700 ms  DeleteObjects: 9.681800 ms)

========================================================================
Received Import Request.
  Time since last request: 1011.249072 seconds.
  path: Assets/OwnMatch3/Scripts/Main/BoardPerformanceConfig.cs
  artifactKey: Guid(41ee0c09b3649c54888a2287287e900c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/OwnMatch3/Scripts/Main/BoardPerformanceConfig.cs using Guid(41ee0c09b3649c54888a2287287e900c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c9ee7345037aa8c1cd67cb0c1006709b') in 0.2912543 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/GemHunterMatch/Prefabs/TieBlock.prefab
  artifactKey: Guid(ace1ff97c2fafdc4d843c2af94c57f0b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/GemHunterMatch/Prefabs/TieBlock.prefab using Guid(ace1ff97c2fafdc4d843c2af94c57f0b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '691adc69d53fa1202a1b5f7d2953f20b') in 0.2468258 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/GemHunterMatch/Prefabs/BonusGems/SmallBomb.prefab
  artifactKey: Guid(dbe7c46f3160232468d718c1745de67f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/GemHunterMatch/Prefabs/BonusGems/SmallBomb.prefab using Guid(dbe7c46f3160232468d718c1745de67f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5686a20758b6b5411b0f286b933c72b5') in 0.205415 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/OwnMatch3/Bonus Item/Bonus Effects/RocketExplosion.prefab
  artifactKey: Guid(820bb51ab3af2d044b9ab9825e67bc1d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/OwnMatch3/Bonus Item/Bonus Effects/RocketExplosion.prefab using Guid(820bb51ab3af2d044b9ab9825e67bc1d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2d9cf8e4db3f5ee911e1ec08594fbf10') in 0.0140881 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/GemHunterMatch/Prefabs/BonusGems/ColorBonus.prefab
  artifactKey: Guid(3c5c8f363a8e0264da69abbe71c4cf40) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/GemHunterMatch/Prefabs/BonusGems/ColorBonus.prefab using Guid(3c5c8f363a8e0264da69abbe71c4cf40) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9c465d7a901b0287063e6622c4215651') in 0.2117283 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/OwnMatch3/Bonus Item/Bonus Effects/GemEffect.prefab
  artifactKey: Guid(87d01e8af8cdf5143a169a7714084052) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/OwnMatch3/Bonus Item/Bonus Effects/GemEffect.prefab using Guid(87d01e8af8cdf5143a169a7714084052) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8b292201add02cb6f1f3b5968a8e81ef') in 0.0191774 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/GemHunterMatch/Prefabs/fluffyGreenGem.prefab
  artifactKey: Guid(924c8da2c1342cd41a99abdae62388df) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/GemHunterMatch/Prefabs/fluffyGreenGem.prefab using Guid(924c8da2c1342cd41a99abdae62388df) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b52d9e1ead41febf7af8124159612146') in 0.0247029 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/GemHunterMatch/Prefabs/IceObstacleGem.prefab
  artifactKey: Guid(bddd9137ca220b646b83da3a2d807b3b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/GemHunterMatch/Prefabs/IceObstacleGem.prefab using Guid(bddd9137ca220b646b83da3a2d807b3b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '27356ee3c189a0528d8c6c03faaf51c0') in 0.0175583 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/GemHunterMatch/Prefabs/FluffyOrangeGem.prefab
  artifactKey: Guid(d58c32f46d12e81498ed381eb549a6ef) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/GemHunterMatch/Prefabs/FluffyOrangeGem.prefab using Guid(d58c32f46d12e81498ed381eb549a6ef) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1f551b229ccb807ea24d964613353767') in 0.0174108 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/GemHunterMatch/Prefabs/fluffyPinkGem.prefab
  artifactKey: Guid(f3e47e37930ff8f48b693da87b4025c7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/GemHunterMatch/Prefabs/fluffyPinkGem.prefab using Guid(f3e47e37930ff8f48b693da87b4025c7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f71402bffb2e675e3e1349a341e14710') in 0.01771 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/GemHunterMatch/Prefabs/LightRotator.prefab
  artifactKey: Guid(a14b0135ba183b74cbf13f436cdd5c0e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/GemHunterMatch/Prefabs/LightRotator.prefab using Guid(a14b0135ba183b74cbf13f436cdd5c0e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8fc2e7f673529b096ceb19a5282b9be9') in 0.0283584 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/GemHunterMatch/Prefabs/Bush.prefab
  artifactKey: Guid(cfb732f52d7770f4681c879a928601ed) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/GemHunterMatch/Prefabs/Bush.prefab using Guid(cfb732f52d7770f4681c879a928601ed) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f99cf5a1d1b344fa967144dd3a30e65b') in 0.0209525 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/OwnMatch3/Scripts/Utils/PerformanceMonitorUI.cs
  artifactKey: Guid(e69a92344c5ee454b8a97a512358585d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/OwnMatch3/Scripts/Utils/PerformanceMonitorUI.cs using Guid(e69a92344c5ee454b8a97a512358585d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '49dd3df3081f41926e4317f247d4f0d5') in 0.0154204 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/GemHunterMatch/Prefabs/WoodenCrate.prefab
  artifactKey: Guid(0a54b5a0014ab394c8acc998e5cec84b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/GemHunterMatch/Prefabs/WoodenCrate.prefab using Guid(0a54b5a0014ab394c8acc998e5cec84b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2ce9be2947e02a3d6ef4f5e25aae2697') in 0.0170454 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/GemHunterMatch/Prefabs/FluffyBlueGem.prefab
  artifactKey: Guid(89982b40a2be78246a4e10a1035caf7d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/GemHunterMatch/Prefabs/FluffyBlueGem.prefab using Guid(89982b40a2be78246a4e10a1035caf7d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '581cd7f411990a9dc0b83345297d39ea') in 0.0325514 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/OwnMatch3/Scripts/Main/Match3Board.cs
  artifactKey: Guid(283ecda25cf9c6141b60f7eabddb25dc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/OwnMatch3/Scripts/Main/Match3Board.cs using Guid(283ecda25cf9c6141b60f7eabddb25dc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '87cc9800b702311c08fa19301af6a867') in 0.0099704 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

