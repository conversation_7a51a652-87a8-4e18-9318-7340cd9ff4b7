Using pre-set license
Built from '6000.2/staging' branch; Version is '6000.2.0b9 (377f5a9787ef) revision 3637082'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 32596 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-07-26T11:15:04Z

COMMAND LINE ARGUMENTS:
F:\Unity Installs\6000.2.0b9\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
F:/Match2D
-logFile
Logs/AssetImportWorker0.log
-srvPort
1867
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: F:/Match2D
F:/Match2D
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [12316]  Target information:

Player connection [12316]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2947594282 [EditorId] 2947594282 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-8GO5TD1) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [12316] Host joined multi-casting on [***********:54997]...
Player connection [12316] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 17.19 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 1.83 ms.
Initialize engine version: 6000.2.0b9 (377f5a9787ef)
[Subsystems] Discovering subsystems at path F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/Match2D/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1660 SUPER (ID=0x21c4)
    Vendor:   NVIDIA
    VRAM:     5966 MB
    Driver:   32.0.15.7657
Initialize mono
Mono path[0] = 'F:/Unity Installs/6000.2.0b9/Editor/Data/Managed'
Mono path[1] = 'F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56520
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.001761 seconds.
- Loaded All Assemblies, in  0.427 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 205 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.632 seconds
Domain Reload Profiling: 1059ms
	BeginReloadAssembly (138ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (200ms)
		LoadAssemblies (138ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (197ms)
			TypeCache.Refresh (196ms)
				TypeCache.ScanAssembly (182ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (633ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (561ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (332ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (61ms)
			ProcessInitializeOnLoadAttributes (118ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.103 seconds
Refreshing native plugins compatible for Editor in 8.94 ms, found 9 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
GPGSUpgrader start
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:38)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 38)

GPGSUpgrader done
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:57)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 57)

Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <e887908fdf9b4028bc879f0c1572eba4>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Refreshing native plugins compatible for Editor in 8.69 ms, found 9 plugins.
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.234 seconds
Domain Reload Profiling: 2336ms
	BeginReloadAssembly (229ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (39ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (794ms)
		LoadAssemblies (469ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (435ms)
			TypeCache.Refresh (308ms)
				TypeCache.ScanAssembly (282ms)
			BuildScriptInfoCaches (111ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1234ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1043ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (197ms)
			ProcessInitializeOnLoadAttributes (539ms)
			ProcessInitializeOnLoadMethodAttributes (288ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 9.58 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.09 ms.
Unloading 70 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10474 unused Assets / (17.3 MB). Loaded Objects now: 13182.
Memory consumption went from 287.5 MB to 270.2 MB.
Total: 19.306500 ms (FindLiveObjects: 1.272400 ms CreateObjectMapping: 1.881500 ms MarkObjects: 8.494400 ms  DeleteObjects: 7.656900 ms)

========================================================================
Received Import Request.
  Time since last request: 1011.239271 seconds.
  path: Assets/OwnMatch3/Bonus Item/Bonus Effects/TrailEffect.prefab
  artifactKey: Guid(9d401e1246b5bc946889441d152f5b3a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/OwnMatch3/Bonus Item/Bonus Effects/TrailEffect.prefab using Guid(9d401e1246b5bc946889441d152f5b3a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8bac24c76f8591a32de22ee4354317fb') in 0.3024215 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/GemHunterMatch/Resources/GameManager.prefab
  artifactKey: Guid(dd27b66b35b0829499d16e86f243c372) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/GemHunterMatch/Resources/GameManager.prefab using Guid(dd27b66b35b0829499d16e86f243c372) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cb78716ed49d1b4a925d1b2aeee77b05') in 0.1486057 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 97

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/GemHunterMatch/Prefabs/fluffyYellowGem.prefab
  artifactKey: Guid(8cc6bc4f5802b33419a96e85fb84e771) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/GemHunterMatch/Prefabs/fluffyYellowGem.prefab using Guid(8cc6bc4f5802b33419a96e85fb84e771) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1c25fd29f687fad99cb23447b19a39c4') in 0.0620531 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/OwnMatch3/Scripts/Main/BoardPerformanceOptimizations.cs
  artifactKey: Guid(7a0cd9ff8861a244b813884843306f37) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/OwnMatch3/Scripts/Main/BoardPerformanceOptimizations.cs using Guid(7a0cd9ff8861a244b813884843306f37) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dd02d40c88c93cab54837a5c424cb159') in 0.0145859 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/GemHunterMatch/Prefabs/BonusGems/FishJump.prefab
  artifactKey: Guid(8ab737c78a283744da1bbbfdc3336eb7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/GemHunterMatch/Prefabs/BonusGems/FishJump.prefab using Guid(8ab737c78a283744da1bbbfdc3336eb7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e5490f200e6e364ece42a7009afff215') in 0.0349534 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/GemHunterMatch/Prefabs/BonusGems/LargeBomb.prefab
  artifactKey: Guid(62690df8ee12255499459d65a63d29be) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/GemHunterMatch/Prefabs/BonusGems/LargeBomb.prefab using Guid(62690df8ee12255499459d65a63d29be) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd56ddeefbd79a24c0a878af5fcc64444') in 0.2106844 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/GemHunterMatch/Prefabs/UIDocument.prefab
  artifactKey: Guid(fa1a28deaea064248aef603a21a002f9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/GemHunterMatch/Prefabs/UIDocument.prefab using Guid(fa1a28deaea064248aef603a21a002f9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'afddb1e5dea3b075ef665d9f145c03e4') in 0.078836 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 79

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/OwnMatch3/Scripts/Match3Logic/BoardLogic.cs
  artifactKey: Guid(d9d37708b1101264ca3a8c09d90cb610) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/OwnMatch3/Scripts/Match3Logic/BoardLogic.cs using Guid(d9d37708b1101264ca3a8c09d90cb610) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ef587112125ae2040b5dc0cf1296c265') in 0.0108884 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/GemHunterMatch/Prefabs/fluffyRedGem.prefab
  artifactKey: Guid(bb60bc505e820734584f3d8bd0a16f53) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/GemHunterMatch/Prefabs/fluffyRedGem.prefab using Guid(bb60bc505e820734584f3d8bd0a16f53) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'caefe9ed7d5852ea8faf18cc0cfdabb1') in 0.026158 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/OwnMatch3/Scripts/Main/README_PerformanceOptimizations.md
  artifactKey: Guid(fe9aa081b4fc73a468c4b685fba43d82) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/OwnMatch3/Scripts/Main/README_PerformanceOptimizations.md using Guid(fe9aa081b4fc73a468c4b685fba43d82) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c9c6b64b96551ddbbf1897c6e2344424') in 0.0142182 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/GemHunterMatch/Prefabs/BonusGems/HorizontalBonus.prefab
  artifactKey: Guid(d3d488c116784e14f8d9926759659256) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/GemHunterMatch/Prefabs/BonusGems/HorizontalBonus.prefab using Guid(d3d488c116784e14f8d9926759659256) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5b255492d5fd50c6aaf25f8cc614c194') in 0.203359 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/GemHunterMatch/Prefabs/BonusGems/VerticalBonus.prefab
  artifactKey: Guid(0060243b43e180d4691da3a5d51dfca2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/GemHunterMatch/Prefabs/BonusGems/VerticalBonus.prefab using Guid(0060243b43e180d4691da3a5d51dfca2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0643261c18eb19ad4b8e3185c8dde812') in 0.226378 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

