Using pre-set license
Built from '6000.2/staging' branch; Version is '6000.2.0b9 (377f5a9787ef) revision 3637082'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 32596 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-07-25T15:49:52Z

COMMAND LINE ARGUMENTS:
F:\Unity Installs\6000.2.0b9\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
F:/Match2D
-logFile
Logs/AssetImportWorker1.log
-srvPort
11052
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: F:/Match2D
F:/Match2D
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [6092]  Target information:

Player connection [6092]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 3285550234 [EditorId] 3285550234 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-8GO5TD1) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [6092] Host joined multi-casting on [***********:54997]...
Player connection [6092] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 16.13 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 1.46 ms.
Initialize engine version: 6000.2.0b9 (377f5a9787ef)
[Subsystems] Discovering subsystems at path F:/Unity Installs/6000.2.0b9/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/Match2D/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1660 SUPER (ID=0x21c4)
    Vendor:   NVIDIA
    VRAM:     5966 MB
    Driver:   32.0.15.7657
Initialize mono
Mono path[0] = 'F:/Unity Installs/6000.2.0b9/Editor/Data/Managed'
Mono path[1] = 'F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'F:/Unity Installs/6000.2.0b9/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56020
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.001615 seconds.
- Loaded All Assemblies, in  0.395 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 206 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.614 seconds
Domain Reload Profiling: 1008ms
	BeginReloadAssembly (118ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (186ms)
		LoadAssemblies (118ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (181ms)
			TypeCache.Refresh (179ms)
				TypeCache.ScanAssembly (168ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (614ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (548ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (320ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (55ms)
			ProcessInitializeOnLoadAttributes (116ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.024 seconds
Refreshing native plugins compatible for Editor in 9.10 ms, found 9 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
GPGSUpgrader start
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:38)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 38)

GPGSUpgrader done
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:57)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 57)

Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <e887908fdf9b4028bc879f0c1572eba4>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Refreshing native plugins compatible for Editor in 10.83 ms, found 9 plugins.
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.175 seconds
Domain Reload Profiling: 2199ms
	BeginReloadAssembly (163ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (29ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (773ms)
		LoadAssemblies (410ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (452ms)
			TypeCache.Refresh (332ms)
				TypeCache.ScanAssembly (308ms)
			BuildScriptInfoCaches (100ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1176ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1007ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (192ms)
			ProcessInitializeOnLoadAttributes (520ms)
			ProcessInitializeOnLoadMethodAttributes (279ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 10.19 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.07 ms.
Unloading 70 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10471 unused Assets / (22.0 MB). Loaded Objects now: 13180.
Memory consumption went from 288.1 MB to 266.1 MB.
Total: 19.645900 ms (FindLiveObjects: 1.626200 ms CreateObjectMapping: 1.770000 ms MarkObjects: 8.140700 ms  DeleteObjects: 8.107700 ms)

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 28.55 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.10 ms.
Unloading 64 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10378 unused Assets / (22.4 MB). Loaded Objects now: 13180.
Memory consumption went from 273.3 MB to 251.0 MB.
Total: 29.165200 ms (FindLiveObjects: 1.921800 ms CreateObjectMapping: 2.195200 ms MarkObjects: 13.116900 ms  DeleteObjects: 11.928800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 10.85 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.09 ms.
Unloading 64 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10378 unused Assets / (21.9 MB). Loaded Objects now: 13180.
Memory consumption went from 271.9 MB to 250.0 MB.
Total: 20.257100 ms (FindLiveObjects: 1.464300 ms CreateObjectMapping: 1.716700 ms MarkObjects: 8.277900 ms  DeleteObjects: 8.796600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 13.47 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.09 ms.
Unloading 64 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10378 unused Assets / (24.1 MB). Loaded Objects now: 13180.
Memory consumption went from 271.9 MB to 247.8 MB.
Total: 27.187800 ms (FindLiveObjects: 1.327100 ms CreateObjectMapping: 2.301500 ms MarkObjects: 11.116300 ms  DeleteObjects: 12.441200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 22100.431080 seconds.
  path: Assets/OwnMatch3/Scripts/Utils/CameraManager.cs
  artifactKey: Guid(e1f8deee91776f043a53ab83ec56a452) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/OwnMatch3/Scripts/Utils/CameraManager.cs using Guid(e1f8deee91776f043a53ab83ec56a452) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '45d416c20d40483fba4bf08215602aea') in 0.3974071 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 9.54 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.08 ms.
Unloading 64 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10378 unused Assets / (21.6 MB). Loaded Objects now: 13182.
Memory consumption went from 272.5 MB to 250.9 MB.
Total: 22.738700 ms (FindLiveObjects: 1.373000 ms CreateObjectMapping: 1.864700 ms MarkObjects: 9.215600 ms  DeleteObjects: 10.283100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 17.19 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.13 ms.
Unloading 64 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10378 unused Assets / (23.3 MB). Loaded Objects now: 13182.
Memory consumption went from 272.6 MB to 249.3 MB.
Total: 26.617100 ms (FindLiveObjects: 1.396700 ms CreateObjectMapping: 1.908800 ms MarkObjects: 12.844200 ms  DeleteObjects: 10.465100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 13.43 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.09 ms.
Unloading 64 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10378 unused Assets / (23.5 MB). Loaded Objects now: 13182.
Memory consumption went from 272.6 MB to 249.1 MB.
Total: 23.921800 ms (FindLiveObjects: 1.446500 ms CreateObjectMapping: 2.177900 ms MarkObjects: 9.167200 ms  DeleteObjects: 11.128200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.153 seconds
Refreshing native plugins compatible for Editor in 7.28 ms, found 9 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
GPGSUpgrader start
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:38)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 38)

GPGSUpgrader done
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GooglePlayGames.Editor.GPGSUpgrader:.cctor () (at Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs:57)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/GooglePlayGames/com.google.play.games/Editor/GPGSUpgrader.cs Line: 57)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <e887908fdf9b4028bc879f0c1572eba4>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Refreshing native plugins compatible for Editor in 9.71 ms, found 9 plugins.
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.085 seconds
Domain Reload Profiling: 4245ms
	BeginReloadAssembly (275ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (109ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (2817ms)
		LoadAssemblies (2618ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (287ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (254ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1086ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (887ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (224ms)
			ProcessInitializeOnLoadAttributes (454ms)
			ProcessInitializeOnLoadMethodAttributes (190ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 9.18 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.08 ms.
Unloading 64 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10470 unused Assets / (22.9 MB). Loaded Objects now: 13186.
Memory consumption went from 288.0 MB to 265.1 MB.
Total: 20.227200 ms (FindLiveObjects: 1.342500 ms CreateObjectMapping: 1.760400 ms MarkObjects: 7.910600 ms  DeleteObjects: 9.212100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 9.79 ms, found 9 plugins.
Preloading 1 native plugins for Editor in 0.11 ms.
Unloading 64 Unused Serialized files (Serialized files now loaded: 0)
Unloading 64 unused Assets / (13.0 MB). Loaded Objects now: 13186.
Memory consumption went from 271.0 MB to 258.0 MB.
Total: 46.343100 ms (FindLiveObjects: 1.262900 ms CreateObjectMapping: 0.542800 ms MarkObjects: 42.655700 ms  DeleteObjects: 1.879600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 22302.190441 seconds.
  path: Assets/OwnMatch3/Fonts/Cute Font/Orange Gummy SDF.asset
  artifactKey: Guid(9037111c6ef22c046acea3cd18809c40) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/OwnMatch3/Fonts/Cute Font/Orange Gummy SDF.asset using Guid(9037111c6ef22c046acea3cd18809c40) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter)
=================================================================
	Native Crash Reporting
=================================================================
Got a UNKNOWN while executing native code. This usually indicates
a fatal error in the mono runtime or one of the native libraries 
used by your application.
=================================================================

=================================================================
	Managed Stacktrace:
=================================================================
	  at <unknown> <0xffffffff>
	  at System.Reflection.RuntimePropertyInfo:get_property_info <0x0007c>
	  at System.Reflection.RuntimePropertyInfo:CachePropertyInfo <0x00042>
	  at System.Reflection.RuntimePropertyInfo:get_PropertyType <0x00022>
	  at UnityEditor.Rendering.Universal.MaterialReferenceBuilder:HasMaterialProperty <0x00017>
	  at System.Linq.Enumerable:Any <0x00096>
	  at <>c:<GetMaterialReferenceLookup>b__2_0 <0x0009a>
	  at WhereEnumerableIterator`1:MoveNext <0x000ea>
	  at UnityEditor.Rendering.Universal.MaterialReferenceBuilder:GetMaterialReferenceLookup <0x00478>
	  at UnityEditor.Rendering.Universal.MaterialReferenceBuilder:.cctor <0x00032>
	  at System.Object:runtime_invoke_void <0x00084>
	  at <unknown> <0xffffffff>
	  at System.Object:__icall_wrapper_mono_generic_class_init <0x0006a>
	  at UnityEditor.Rendering.Universal.ConversionIndexers:ConversionIndexer <0x0006a>
	  at <Module>:invoke_void_CustomObjectIndexerTarget_ObjectIndexer <0x00113>
	  at UnityEditor.Search.ObjectIndexer:CallCustomIndexers <0x004c9>
	  at UnityEditor.Search.ObjectIndexer:IndexCustomProperties <0x00132>
	  at UnityEditor.Search.AssetIndexer:IndexSubAsset <0x003fa>
	  at UnityEditor.Search.AssetIndexer:IndexDocument <0x00b92>
	  at UnityEditor.Search.SearchIndexEntryImporter:OnImportAsset <0x00163>
	  at UnityEditor.AssetImporters.ScriptedImporter:GenerateAssetData <0x00018>
	  at <Module>:runtime_invoke_void__this___object <0x00092>
=================================================================
 -> (artifact id: 'eeeb7c0e0b43feb9c2e39d86add1e503') in 0.5894244 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

