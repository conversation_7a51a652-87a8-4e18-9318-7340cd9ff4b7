using UnityEngine;
using System.Collections;
using OwnMatch3.Utils;

namespace OwnMatch3.Utils
{
    /// <summary>
    /// Simple performance test to verify optimization effectiveness
    /// </summary>
    public class PerformanceTestSimple : MonoBehaviour
    {
        [Header("Test Settings")]
        [SerializeField] private bool runTestOnStart = false;
        [SerializeField] private bool showFPSCounter = true;
        [SerializeField] private int testDuration = 30; // seconds
        
        [Header("Performance Monitoring")]
        [SerializeField] private float frameTimeThreshold = 16.67f; // 60 FPS = 16.67ms
        [SerializeField] private bool logFrameDrops = true;
        
        private Match3Board board;
        private float lastFrameTime;
        private int frameDropCount = 0;
        private int totalFrames = 0;
        private float testStartTime;
        private bool isTestingActive = false;
        
        // UI Display
        private GUIStyle guiStyle;
        
        private void Start()
        {
            board = FindFirstObjectByType<Match3Board>();
            if (board == null)
            {
                DebugManager.LogMatchError("[PerformanceTestSimple] Match3Board not found!");
                return;
            }
            
            // Initialize GUI style
            guiStyle = new GUIStyle();
            guiStyle.fontSize = 24;
            guiStyle.normal.textColor = Color.white;
            
            if (runTestOnStart)
            {
                StartPerformanceTest();
            }
        }
        
        private void Update()
        {
            if (isTestingActive)
            {
                MonitorPerformance();
            }
        }
        
        private void OnGUI()
        {
            if (showFPSCounter)
            {
                DisplayPerformanceInfo();
            }
        }
        
        /// <summary>
        /// Start performance monitoring test
        /// </summary>
        [ContextMenu("Start Performance Test")]
        public void StartPerformanceTest()
        {
            if (board == null) return;
            
            DebugManager.LogMatch("[PerformanceTestSimple] Starting performance test...");
            
            // Reset counters
            frameDropCount = 0;
            totalFrames = 0;
            testStartTime = Time.time;
            isTestingActive = true;
            
            // Enable optimizations for testing
            board.SetPerformanceOptimizations(true);
            board.SetSimpleOptimizations(true);
            
            // Stop test after duration
            StartCoroutine(StopTestAfterDuration());
        }
        
        /// <summary>
        /// Stop performance test
        /// </summary>
        [ContextMenu("Stop Performance Test")]
        public void StopPerformanceTest()
        {
            if (!isTestingActive) return;
            
            isTestingActive = false;
            
            float testTime = Time.time - testStartTime;
            float averageFPS = totalFrames / testTime;
            float frameDropPercentage = (float)frameDropCount / totalFrames * 100f;
            
            DebugManager.LogMatch($"[PerformanceTestSimple] Test Results:");
            DebugManager.LogMatch($"  Test Duration: {testTime:F1}s");
            DebugManager.LogMatch($"  Total Frames: {totalFrames}");
            DebugManager.LogMatch($"  Average FPS: {averageFPS:F1}");
            DebugManager.LogMatch($"  Frame Drops: {frameDropCount} ({frameDropPercentage:F1}%)");
            DebugManager.LogMatch($"  Performance: {(frameDropPercentage < 5f ? "GOOD" : frameDropPercentage < 15f ? "ACCEPTABLE" : "POOR")}");
        }
        
        /// <summary>
        /// Monitor frame performance
        /// </summary>
        private void MonitorPerformance()
        {
            float currentFrameTime = Time.unscaledDeltaTime * 1000f; // Convert to milliseconds
            totalFrames++;
            
            // Check for frame drops
            if (currentFrameTime > frameTimeThreshold)
            {
                frameDropCount++;
                
                if (logFrameDrops)
                {
                    DebugManager.LogMatchWarning($"[PerformanceTestSimple] Frame drop detected: {currentFrameTime:F1}ms (Target: {frameTimeThreshold:F1}ms)");
                }
            }
            
            lastFrameTime = currentFrameTime;
        }
        
        /// <summary>
        /// Display performance information on screen
        /// </summary>
        private void DisplayPerformanceInfo()
        {
            float currentFPS = 1f / Time.unscaledDeltaTime;
            
            string performanceText = $"FPS: {currentFPS:F1}\n";
            performanceText += $"Frame Time: {lastFrameTime:F1}ms\n";
            
            if (isTestingActive)
            {
                float testTime = Time.time - testStartTime;
                float frameDropPercentage = totalFrames > 0 ? (float)frameDropCount / totalFrames * 100f : 0f;
                
                performanceText += $"Test Time: {testTime:F1}s\n";
                performanceText += $"Frame Drops: {frameDropCount} ({frameDropPercentage:F1}%)\n";
                
                // Color code based on performance
                if (frameDropPercentage < 5f)
                {
                    guiStyle.normal.textColor = Color.green;
                }
                else if (frameDropPercentage < 15f)
                {
                    guiStyle.normal.textColor = Color.yellow;
                }
                else
                {
                    guiStyle.normal.textColor = Color.red;
                }
            }
            else
            {
                // Color code based on current FPS
                if (currentFPS >= 55f)
                {
                    guiStyle.normal.textColor = Color.green;
                }
                else if (currentFPS >= 30f)
                {
                    guiStyle.normal.textColor = Color.yellow;
                }
                else
                {
                    guiStyle.normal.textColor = Color.red;
                }
            }
            
            // Display optimization status
            if (board != null)
            {
                var (enabled, batchSize, delay) = board.GetSimpleOptimizationSettings();
                performanceText += $"\nOptimizations: {(enabled ? "ON" : "OFF")}\n";
                if (enabled)
                {
                    performanceText += $"Batch Size: {batchSize}\n";
                    performanceText += $"Batch Delay: {delay * 1000:F0}ms";
                }
            }
            
            GUI.Label(new Rect(10, 10, 300, 200), performanceText, guiStyle);
        }
        
        /// <summary>
        /// Stop test after specified duration
        /// </summary>
        private IEnumerator StopTestAfterDuration()
        {
            yield return new WaitForSeconds(testDuration);
            StopPerformanceTest();
        }
        
        /// <summary>
        /// Toggle optimizations for comparison
        /// </summary>
        [ContextMenu("Toggle Optimizations")]
        public void ToggleOptimizations()
        {
            if (board == null) return;
            
            var (enabled, _, _) = board.GetSimpleOptimizationSettings();
            board.SetSimpleOptimizations(!enabled);
            
            DebugManager.LogMatch($"[PerformanceTestSimple] Simple optimizations {(!enabled ? "enabled" : "disabled")}");
        }
        
        /// <summary>
        /// Test with original system (no optimizations)
        /// </summary>
        [ContextMenu("Test Original System")]
        public void TestOriginalSystem()
        {
            if (board == null) return;
            
            board.SetPerformanceOptimizations(false);
            board.SetSimpleOptimizations(false);
            StartPerformanceTest();
            
            DebugManager.LogMatch("[PerformanceTestSimple] Testing original system (no optimizations)");
        }
        
        /// <summary>
        /// Test with optimized system
        /// </summary>
        [ContextMenu("Test Optimized System")]
        public void TestOptimizedSystem()
        {
            if (board == null) return;
            
            board.SetPerformanceOptimizations(true);
            board.SetSimpleOptimizations(true);
            StartPerformanceTest();
            
            DebugManager.LogMatch("[PerformanceTestSimple] Testing optimized system");
        }
        
        /// <summary>
        /// Get current performance status
        /// </summary>
        public (float fps, float frameTime, int frameDrops, bool optimized) GetCurrentStatus()
        {
            float currentFPS = 1f / Time.unscaledDeltaTime;
            var (optimized, _, _) = board?.GetSimpleOptimizationSettings() ?? (false, 0, 0f);
            
            return (currentFPS, lastFrameTime, frameDropCount, optimized);
        }
    }
}
