-target:library
-out:"Library/Bee/artifacts/1300b0aE.dag/OwnMatch3.dll"
-refout:"Library/Bee/artifacts/1300b0aE.dag/OwnMatch3.ref.dll"
-define:UNITY_6000_2_0
-define:UNITY_6000_2
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:UNITY_6000_1_OR_NEWER
-define:UNITY_6000_2_OR_NEWER
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_UNITY_CONSENT
-define:ENABLE_UNITY_CLOUD_IDENTIFIERS
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_ENGINE_CODE_STRIPPING
-define:ENABLE_ONSCREEN_KEYBOARD
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:ENABLE_ACCESSIBILITY
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_ANDROID
-define:UNITY_ANDROID
-define:UNITY_ANDROID_API
-define:ENABLE_EGL
-define:ENABLE_NETWORK
-define:ENABLE_RUNTIME_GI
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_CLOUD_SERVICES_ENGINE_DIAGNOSTICS
-define:ENABLE_FIREBASE_IDENTIFIERS
-define:UNITY_CAN_SHOW_SPLASH_SCREEN
-define:UNITY_HAS_GOOGLEVR
-define:UNITY_HAS_TANGO
-define:ENABLE_SPATIALTRACKING
-define:ENABLE_ETC_COMPRESSION
-define:PLATFORM_EXTENDS_VULKAN_DEVICE
-define:PLATFORM_HAS_MULTIPLE_SWAPCHAINS
-define:UNITY_ANDROID_SUPPORTS_SHADOWFILES
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:PLATFORM_EXTENDS_VULKAN_PIPELINE_CACHE
-define:PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS
-define:PLATFORM_HAS_ADDITIONAL_API_CHECKS
-define:PLATFORM_HAS_GRAPHICS_JOBS_SUPPORT_CHECK_OVERRIDE
-define:PLATFORM_IMPLEMENTS_INSIGHTS_ANR
-define:ENABLE_ANDROID_ADVERTISING_IDS
-define:PLATFORM_HAS_BUGGY_MSAA_RESOLVE
-define:ENABLE_UNITYADS_RUNTIME
-define:UNITY_UNITYADS_API
-define:ENABLE_MONO
-define:NET_STANDARD_2_0
-define:NET_STANDARD
-define:NET_STANDARD_2_1
-define:NETSTANDARD
-define:NETSTANDARD2_1
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:PRIME_TWEEN_SAFETY_CHECKS
-define:LEVELPLAY_DEPENDENCIES_INSTALLED
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"Assets/ExternalDependencyManager/Editor/1.2.182/Google.IOSResolver.dll"
-r:"Assets/ExternalDependencyManager/Editor/1.2.182/Google.JarResolver.dll"
-r:"Assets/ExternalDependencyManager/Editor/1.2.182/Google.PackageManagerResolver.dll"
-r:"Assets/ExternalDependencyManager/Editor/1.2.182/Google.VersionHandlerImpl.dll"
-r:"Assets/ExternalDependencyManager/Editor/Google.VersionHandler.dll"
-r:"Assets/Plugins/System.Runtime.CompilerServices.Unsafe.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEditor.Graphs.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.ClothModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.IdentifiersModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.InsightsModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConsentModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/NetStandard/ref/2.1.0/netstandard.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll"
-r:"F:/Unity Installs/6000.2.0b9/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll"
-r:"Library/PackageCache/com.unity.ai.toolkit@97783faabe3b/Modules/Sdk/Editor/AiEditorToolsSdk.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/log4netPlastic.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/Unity.Plastic.Antlr3.Runtime.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/Unity.Plastic.Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/unityplastic.dll"
-r:"Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.IO.Hashing/System.IO.Hashing.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@d78732e851eb/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Editor/VisualScripting.Core/Dependencies/DotNetZip/Unity.VisualScripting.IonicZip.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Editor/VisualScripting.Core/Dependencies/YamlDotNet/Unity.VisualScripting.YamlDotNet.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Editor/VisualScripting.Core/EditorAssetResources/Unity.VisualScripting.TextureAssets.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/CFXRRuntime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/CryptoLibrary.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/EventFramework.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Google.Play.AppUpdate.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Google.Play.Common.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Google.Play.Games.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/PrimeTween.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/UniTask.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.SpriteShape.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.LevelPlay.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/ZString.ref.dll"
-analyzer:"F:/Unity Installs/6000.2.0b9/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"F:/Unity Installs/6000.2.0b9/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"F:/Unity Installs/6000.2.0b9/Editor/Data/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Assets/OwnMatch3/PlayerInput.cs"
"Assets/OwnMatch3/Scripts/AI/AdaptiveComponents.cs"
"Assets/OwnMatch3/Scripts/AI/AdaptiveConfig.cs"
"Assets/OwnMatch3/Scripts/AI/AdaptiveGenerationSystem.cs"
"Assets/OwnMatch3/Scripts/AI/AIGenerationStructures.cs"
"Assets/OwnMatch3/Scripts/AI/AILevelGenerator.cs"
"Assets/OwnMatch3/Scripts/AI/AILevelGeneratorIntegration.cs"
"Assets/OwnMatch3/Scripts/AI/AILevelIntegration.cs"
"Assets/OwnMatch3/Scripts/AI/DifficultyCalculator.cs"
"Assets/OwnMatch3/Scripts/AI/LevelAnalyzer.cs"
"Assets/OwnMatch3/Scripts/AI/LevelDataConverter.cs"
"Assets/OwnMatch3/Scripts/AI/LevelLoaderExtensions.cs"
"Assets/OwnMatch3/Scripts/AI/LevelPatterns.cs"
"Assets/OwnMatch3/Scripts/AI/LevelValidator.cs"
"Assets/OwnMatch3/Scripts/AI/PatternMatcher.cs"
"Assets/OwnMatch3/Scripts/Avatars/AvatarConfiguration.cs"
"Assets/OwnMatch3/Scripts/Avatars/AvatarManager.cs"
"Assets/OwnMatch3/Scripts/Bonuses/BombBonus.cs"
"Assets/OwnMatch3/Scripts/Bonuses/BombXBonus.cs"
"Assets/OwnMatch3/Scripts/Bonuses/Bonus.cs"
"Assets/OwnMatch3/Scripts/Bonuses/BonusActivityTracker.cs"
"Assets/OwnMatch3/Scripts/Bonuses/ColorBonus.cs"
"Assets/OwnMatch3/Scripts/Bonuses/FishBonus.cs"
"Assets/OwnMatch3/Scripts/Bonuses/GemDestructionEffect.cs"
"Assets/OwnMatch3/Scripts/Bonuses/LineRendererExtensions.cs"
"Assets/OwnMatch3/Scripts/Bonuses/LineRocketBonus.cs"
"Assets/OwnMatch3/Scripts/Bonuses/RocketExplosionEffect.cs"
"Assets/OwnMatch3/Scripts/Bonuses/RocketTrailEffect.cs"
"Assets/OwnMatch3/Scripts/Boosters/BoosterConfiguration.cs"
"Assets/OwnMatch3/Scripts/Boosters/BoosterManager.cs"
"Assets/OwnMatch3/Scripts/Debug/LevelCountTest.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Components/SecuredTypeAutoFix.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Constants/GeneralConstants.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Constants/GeneralStrings.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/AllowedAssembly.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/AssembliesWhitelist.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/EditorExtensions.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/GameShieldStyles.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/GameShieldWizzard.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/InjectionDetectorGlobal.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/Postprocessor.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/PropertyDrawers/SecuredBoolDrawer.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/PropertyDrawers/SecuredColor32Drawer.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/PropertyDrawers/SecuredColorDrawer.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/PropertyDrawers/SecuredFloatDrawer.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/PropertyDrawers/SecuredIntDrawer.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/PropertyDrawers/SecuredPropertyDrawer.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/PropertyDrawers/SecuredQuaternionDrawer.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/PropertyDrawers/SecuredStringDrawer.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/PropertyDrawers/SecuredVector2Drawer.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/PropertyDrawers/SecuredVector3Drawer.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/PropertyDrawers/SecuredVector4Drawer.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/Tools/AutoResetSecuredTypesOnReload.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/Tools/DebugUnityFieldSerialization.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/Tools/ForceInspectorRefresh.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/Tools/QuickDataFix.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/Tools/QuickSecuredTypeConverter.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/Tools/SafeSecuredTypeConverter.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/Tools/SecuredTypeBatchConverter.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/Tools/SecuredTypeConverter.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/Tools/SecuredTypeConverterDocumentation.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/Tools/SecuredTypeInitializer.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/Tools/SecuredTypePatcher.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/Tools/SecuredTypeReloadFixer.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/Tools/SecuredTypeSettingsWindow.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/Tools/SimpleSecuredTypeFix.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/Tools/SmartSecuredTypeConverter.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/Tools/SourceCodeDefaultValueParser.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/Tools/UltimateSecuredTypeConverter.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/Tools/UnityFieldInitializationFix.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Editor/WizzardTab.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/GameShield.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/GameShieldConfig.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Captcha/RequestCaptchaPayload.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Captcha/RewardedCaptcha.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Captcha/RewardedCaptchaData.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/GameSaves/ISaveObject.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/GameSaves/Payloads/SaveGamePayloads.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/GameSaves/SaveGameData.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/GameSaves/SavesWarnings.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/GameSaves/SecuredSaves.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Injection/AllowedAssembly.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Injection/InjectionScanner.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Injection/InjectionWarnings.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/IShieldModule.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/IShieldModuleConfig.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Memory/MemoryProtector.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Memory/MemoryWarnings.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Memory/SecuredTypes/ImprovedSecuredInt.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Memory/SecuredTypes/SecuredBool.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Memory/SecuredTypes/SecuredByte.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Memory/SecuredTypes/SecuredChar.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Memory/SecuredTypes/SecuredColor.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Memory/SecuredTypes/SecuredColor32.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Memory/SecuredTypes/SecuredDecimal.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Memory/SecuredTypes/SecuredFloat.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Memory/SecuredTypes/SecuredInt.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Memory/SecuredTypes/SecuredIntAttribute.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Memory/SecuredTypes/SecuredLong.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Memory/SecuredTypes/SecuredQuaternion.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Memory/SecuredTypes/SecuredSByte.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Memory/SecuredTypes/SecuredShort.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Memory/SecuredTypes/SecuredString.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Memory/SecuredTypes/SecuredUInt.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Memory/SecuredTypes/SecuredULong.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Memory/SecuredTypes/SecuredUShort.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Memory/SecuredTypes/SecuredVector2.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Memory/SecuredTypes/SecuredVector3.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Memory/SecuredTypes/SecuredVector4.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/ModuleInfo.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/SpeedHack/SpeedHackDetector.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/SpeedHack/SpeedHackWarnings.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Teleport/TeleportDetector.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Teleport/TeleportTargetChecker.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Teleport/TeleportWarnings.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Time/TimeProtector.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Time/TimeWarnings.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/WallHack/WallhackMessages.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/WallHack/WallHackProtector.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Web/SecuredRequest.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Modules/Web/WebRequestData.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Payloads/LifecyclePayloads.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Payloads/SecurityPayloads.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Reporter/ReportData.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Reporter/ReporterWorker.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Reporter/ReportingPayload.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Utils/EnumerableExtensions.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Utils/Generator.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Core/Utils/ModuleManager.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Demo/Scripts/Controllers/CameraController.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Demo/Scripts/Controllers/EnemyController.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Demo/Scripts/Controllers/PlayerController.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Demo/Scripts/Enums/BaseState.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Demo/Scripts/GameInstaller.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Demo/Scripts/Payloads/CameraPayloads.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Demo/Scripts/Payloads/EnemyPayloads.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Demo/Scripts/Payloads/PlayerPayloads.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Demo/Scripts/Payloads/UIPayloads.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Demo/Scripts/UI/BaseView.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Demo/Scripts/UI/CaptchaView.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Demo/Scripts/UI/DetectionView.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Demo/Scripts/UI/DialogView.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Demo/Scripts/UI/Elements/CaptchaButton.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Demo/Scripts/UI/InGameView.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Demo/Scripts/UI/ReportingView.cs"
"Assets/OwnMatch3/Scripts/DevsDaddy/GameShield/Demo/Scripts/UI/WelcomeView.cs"
"Assets/OwnMatch3/Scripts/Editor/CreateAIConfigAssets.cs"
"Assets/OwnMatch3/Scripts/Editor/DebugMigrationWindow.cs"
"Assets/OwnMatch3/Scripts/Editor/EditShapeMatch.cs"
"Assets/OwnMatch3/Scripts/Editor/EncryptionKeyGeneratorWindow.cs"
"Assets/OwnMatch3/Scripts/Editor/GameProgressManagerEditor.cs"
"Assets/OwnMatch3/Scripts/Editor/LevelPreviewComponent.cs"
"Assets/OwnMatch3/Scripts/Editor/MatchShapeValidator.cs"
"Assets/OwnMatch3/Scripts/Editor/ModernAILevelGeneratorWindow.cs"
"Assets/OwnMatch3/Scripts/Editor/ObjectPoolerAutoOptimizer.cs"
"Assets/OwnMatch3/Scripts/Editor/ObjectPoolerEditorWindow.cs"
"Assets/OwnMatch3/Scripts/Editor/ObjectPoolerGenericExamples.cs"
"Assets/OwnMatch3/Scripts/Editor/ObjectPoolerGizmos.cs"
"Assets/OwnMatch3/Scripts/Editor/ObjectPoolerSettings.cs"
"Assets/OwnMatch3/Scripts/Editor/ObjectPoolerToolbar.cs"
"Assets/OwnMatch3/Scripts/Editor/SimpleCameraManagerEditor.cs"
"Assets/OwnMatch3/Scripts/Editor/VideoFrameExtractorWindow.cs"
"Assets/OwnMatch3/Scripts/Effects/BonusGemEffects.cs"
"Assets/OwnMatch3/Scripts/Examples/BonusGemSystemExample.cs"
"Assets/OwnMatch3/Scripts/Examples/BonusGemSystemExampleAdvanced.cs"
"Assets/OwnMatch3/Scripts/Examples/StarAnimationTester.cs"
"Assets/OwnMatch3/Scripts/Gems/GemNew.cs"
"Assets/OwnMatch3/Scripts/Goals/Goal.cs"
"Assets/OwnMatch3/Scripts/Level/LevelData.cs"
"Assets/OwnMatch3/Scripts/Levels/LevelJson.cs"
"Assets/OwnMatch3/Scripts/Levels/LevelLoader.cs"
"Assets/OwnMatch3/Scripts/Levels/LevelLoaderJobs.cs"
"Assets/OwnMatch3/Scripts/Levels/LevelSetup.cs"
"Assets/OwnMatch3/Scripts/Main/AudioManager.cs"
"Assets/OwnMatch3/Scripts/Main/BoardPerformanceConfig.cs"
"Assets/OwnMatch3/Scripts/Main/BoardPerformanceOptimizations.cs"
"Assets/OwnMatch3/Scripts/Main/BoardPerformanceSimple.cs"
"Assets/OwnMatch3/Scripts/Main/BonusGemManager.cs"
"Assets/OwnMatch3/Scripts/Main/CloudSaveExample.cs"
"Assets/OwnMatch3/Scripts/Main/GameProgressManager.cs"
"Assets/OwnMatch3/Scripts/Main/GameStateManager.cs"
"Assets/OwnMatch3/Scripts/Main/GemSoundSystem.cs"
"Assets/OwnMatch3/Scripts/Main/HintMove.cs"
"Assets/OwnMatch3/Scripts/Main/LevelCompletionHandler.cs"
"Assets/OwnMatch3/Scripts/Main/Match3Board.cs"
"Assets/OwnMatch3/Scripts/Main/SaveSystemIntegration.cs"
"Assets/OwnMatch3/Scripts/Main/ScoreManager.cs"
"Assets/OwnMatch3/Scripts/Match3Input/BoardInput.cs"
"Assets/OwnMatch3/Scripts/Match3Logic/BoardLogic.cs"
"Assets/OwnMatch3/Scripts/Match3Logic/BoardShuffleJobs.cs"
"Assets/OwnMatch3/Scripts/Match3Match/BoardMatchFinder.cs"
"Assets/OwnMatch3/Scripts/Match3Match/BoardMatchFinderAsync.cs"
"Assets/OwnMatch3/Scripts/Match3Match/BoardMatchFinderJobs.cs"
"Assets/OwnMatch3/Scripts/Match3Match/BoardMatchFinderOptimized.cs"
"Assets/OwnMatch3/Scripts/Match3Match/BoardMatchResolver.cs"
"Assets/OwnMatch3/Scripts/Match3Match/ShapeMatcher.cs"
"Assets/OwnMatch3/Scripts/Match3Obstacles/ObstacleHandler.cs"
"Assets/OwnMatch3/Scripts/Match3Obstacles/ObstacleTileData.cs"
"Assets/OwnMatch3/Scripts/Match3Setup/BoardJsonInit.cs"
"Assets/OwnMatch3/Scripts/Match3Setup/BoardUtils.cs"
"Assets/OwnMatch3/Scripts/Match3Setup/FrameBuilder.cs"
"Assets/OwnMatch3/Scripts/Match3Setup/ObstacleSoundSystem.cs"
"Assets/OwnMatch3/Scripts/Match3Setup/ObstacleSoundSystemSetup.cs"
"Assets/OwnMatch3/Scripts/MatchInfo.cs"
"Assets/OwnMatch3/Scripts/Monetization/AdConfig.cs"
"Assets/OwnMatch3/Scripts/Monetization/AdReward.cs"
"Assets/OwnMatch3/Scripts/Monetization/AdsGameIntegration.cs"
"Assets/OwnMatch3/Scripts/Monetization/AdsIntegrationHelper.cs"
"Assets/OwnMatch3/Scripts/Monetization/AdsManager.cs"
"Assets/OwnMatch3/Scripts/Monetization/AdsSetupExample.cs"
"Assets/OwnMatch3/Scripts/MoveToNextScene.cs"
"Assets/OwnMatch3/Scripts/ObstacleNew.cs"
"Assets/OwnMatch3/Scripts/Security/BlowFishWrapper.cs"
"Assets/OwnMatch3/Scripts/Security/EncryptionKeyManager.cs"
"Assets/OwnMatch3/Scripts/Security/GameSecurity.cs"
"Assets/OwnMatch3/Scripts/Security/GetTimeNet.cs"
"Assets/OwnMatch3/Scripts/Security/GetTimeNetInitializer.cs"
"Assets/OwnMatch3/Scripts/Security/UpdateGame.cs"
"Assets/OwnMatch3/Scripts/Setup/BonusGemSystemSetup.cs"
"Assets/OwnMatch3/Scripts/Shop/ShopConfiguration.cs"
"Assets/OwnMatch3/Scripts/Shop/ShopDataStructures.cs"
"Assets/OwnMatch3/Scripts/Shop/ShopManager.cs"
"Assets/OwnMatch3/Scripts/Testing/CurrencyTestController.cs"
"Assets/OwnMatch3/Scripts/Testing/SaveSystemDebugger.cs"
"Assets/OwnMatch3/Scripts/Testing/SaveSystemTester.cs"
"Assets/OwnMatch3/Scripts/Testing/TestingSecuredTypes.cs"
"Assets/OwnMatch3/Scripts/UI/CloudSaveStatusUI.cs"
"Assets/OwnMatch3/Scripts/UI/ComboAnnouncer.cs"
"Assets/OwnMatch3/Scripts/UI/ComboManager.cs"
"Assets/OwnMatch3/Scripts/UI/CurrencyAnimationManager.cs"
"Assets/OwnMatch3/Scripts/UI/CurrencyAttractorUI.cs"
"Assets/OwnMatch3/Scripts/UI/DailyRewardsManager.cs"
"Assets/OwnMatch3/Scripts/UI/EmailManager.cs"
"Assets/OwnMatch3/Scripts/UI/EmailUIController.cs"
"Assets/OwnMatch3/Scripts/UI/EndGamePopupController.cs"
"Assets/OwnMatch3/Scripts/UI/GameUI.cs"
"Assets/OwnMatch3/Scripts/UI/GameUIDocument.cs"
"Assets/OwnMatch3/Scripts/UI/GemAttractorUI.cs"
"Assets/OwnMatch3/Scripts/UI/GoalDisplayConfiguration.cs"
"Assets/OwnMatch3/Scripts/UI/LevelSelectionUI.cs"
"Assets/OwnMatch3/Scripts/UI/MenuUIController.cs"
"Assets/OwnMatch3/Scripts/UI/MissionsManager.cs"
"Assets/OwnMatch3/Scripts/UI/MissionsUIController.cs"
"Assets/OwnMatch3/Scripts/UI/MovesWarningAnimation.cs"
"Assets/OwnMatch3/Scripts/UI/Particles/ParticleElement.cs"
"Assets/OwnMatch3/Scripts/UI/Particles/ParticleElementDemo.cs"
"Assets/OwnMatch3/Scripts/UI/Particles/ParticleElementPositionTest.cs"
"Assets/OwnMatch3/Scripts/UI/Particles/ParticleElementPropertyTester.cs"
"Assets/OwnMatch3/Scripts/UI/Particles/ParticleElementSizeTest.cs"
"Assets/OwnMatch3/Scripts/UI/Particles/ParticleElementTester.cs"
"Assets/OwnMatch3/Scripts/UI/Particles/SimpleParticleController.cs"
"Assets/OwnMatch3/Scripts/UI/PointsAnnouncer.cs"
"Assets/OwnMatch3/Scripts/UI/PreGamePopupController.cs"
"Assets/OwnMatch3/Scripts/UI/PreGamePopupSetup.cs"
"Assets/OwnMatch3/Scripts/UI/ProgressDisplayUI.cs"
"Assets/OwnMatch3/Scripts/UI/ResponsiveUIHelper.cs"
"Assets/OwnMatch3/Scripts/UI/ScoreProgressBarController.cs"
"Assets/OwnMatch3/Scripts/UI/ScoreProgressBarExample.cs"
"Assets/OwnMatch3/Scripts/UI/StageProgressBar.cs"
"Assets/OwnMatch3/Scripts/UI/StageProgressBarDebug.cs"
"Assets/OwnMatch3/Scripts/UI/StageProgressBarExample.cs"
"Assets/OwnMatch3/Scripts/UI/StarCalculationTest.cs"
"Assets/OwnMatch3/Scripts/UI/UIManager.cs"
"Assets/OwnMatch3/Scripts/UI/UIUtils.cs"
"Assets/OwnMatch3/Scripts/Utils/CameraManager.cs"
"Assets/OwnMatch3/Scripts/Utils/CameraSetupHelper.cs"
"Assets/OwnMatch3/Scripts/Utils/DebugManager.cs"
"Assets/OwnMatch3/Scripts/Utils/DebugMigrationHelper.cs"
"Assets/OwnMatch3/Scripts/Utils/DebugSettings.cs"
"Assets/OwnMatch3/Scripts/Utils/FPSCounter.cs"
"Assets/OwnMatch3/Scripts/Utils/LogToFile.cs"
"Assets/OwnMatch3/Scripts/Utils/ObjectPooler.cs"
"Assets/OwnMatch3/Scripts/Utils/ObjectPoolerLinqUtils.cs"
"Assets/OwnMatch3/Scripts/Utils/ObjectPoolerMonitor.cs"
"Assets/OwnMatch3/UI/Custom UI Elements/CurvedProgressBar.cs"
"Assets/OwnMatch3/UI/Custom UI Elements/StarRatingElement.cs"
-langversion:9.0
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
/additionalfile:"Library/Bee/artifacts/1300b0aE.dag/OwnMatch3.UnityAdditionalFile.txt"