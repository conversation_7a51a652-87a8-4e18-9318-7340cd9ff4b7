{ "pid": 86996, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "<PERSON><PERSON><PERSON><PERSON>" } },
{ "pid": 86996, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 86996, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen2" } },
{ "pid": 86996, "tid": 12884901888, "ts": 1753478402427196, "dur": 10402, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "LOH allocation", "Type": "Blocking GC"} },
{ "pid": 86996, "tid": 12884901888, "ts": 1753478404895181, "dur": 312415, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "SOH allocation", "Type": "Background GC"} },
{ "pid": 86996, "tid": 12884901888, "ts": 1753478408964103, "dur": 433104, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "LOH allocation", "Type": "Background GC"} },
{ "pid": 86996, "tid": 1, "ts": 1753478409587257, "dur": 2379, "ph": "X", "name": "GC - Gen2", "args": {} },
{ "pid": 86996, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen1" } },
{ "pid": 86996, "tid": 8589934592, "ts": 1753478404896037, "dur": 180651, "ph": "X", "name": "GC - Gen 1", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 86996, "tid": 1, "ts": 1753478409589639, "dur": 10, "ph": "X", "name": "GC - Gen1", "args": {} },
{ "pid": 86996, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen0" } },
{ "pid": 86996, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 86996, "tid": 1, "ts": 1753478399124768, "dur": 10454071, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 86996, "tid": 1, "ts": 1753478399127078, "dur": 380681, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478399353932, "dur": 92847, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478399507762, "dur": 44809, "ph": "X", "name": "RegisterRuntimeEventListeners", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478399727088, "dur": 173808, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478399902727, "dur": 239652, "ph": "X", "name": "SetupAndRegisterUnityEngineSteps", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478400142419, "dur": 1685239, "ph": "X", "name": "ResolveUnityEngine", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478401827678, "dur": 618879, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478402446572, "dur": 605755, "ph": "X", "name": "InitializeEngineStrippingStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403052338, "dur": 3574, "ph": "X", "name": "ResolveForEngineModuleStrippingEnabledStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403055921, "dur": 1066, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403056990, "dur": 2063, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403059056, "dur": 339, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403059401, "dur": 22335, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403081742, "dur": 320, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403082066, "dur": 151, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403082219, "dur": 67, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403082287, "dur": 210, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403082500, "dur": 145, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403082647, "dur": 49, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403082697, "dur": 573, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403083274, "dur": 21711, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403104992, "dur": 472, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403105475, "dur": 9257, "ph": "X", "name": "SetupAndRegisterUnityRootsSteps", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403114757, "dur": 7431, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403122194, "dur": 5485, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403127686, "dur": 55, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403127742, "dur": 1935, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403129682, "dur": 379, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403130063, "dur": 268, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403130334, "dur": 113, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403130448, "dur": 57, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403130507, "dur": 918, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403131427, "dur": 5486, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403136920, "dur": 3548, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403140474, "dur": 5364, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403145845, "dur": 3397, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403149249, "dur": 5649, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403154907, "dur": 8268, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403163185, "dur": 5278, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403168470, "dur": 452, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403168926, "dur": 125, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403169052, "dur": 421, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403169474, "dur": 131, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403169606, "dur": 3439, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403173051, "dur": 3687, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403176742, "dur": 1124, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403177870, "dur": 3993, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403181868, "dur": 882, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403182751, "dur": 2647, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403185401, "dur": 1316, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403186720, "dur": 393, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403187114, "dur": 2191, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403189308, "dur": 906, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403190226, "dur": 1811, "ph": "X", "name": "UnityLoadReferencesStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403192044, "dur": 1293, "ph": "X", "name": "SetupAndRegisterUnityRootsSteps2", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403193343, "dur": 805, "ph": "X", "name": "SetupI18N", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403194153, "dur": 34912, "ph": "X", "name": "ResolveFromDescriptorsStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403229101, "dur": 172, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403229276, "dur": 158, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403229435, "dur": 8251, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403237690, "dur": 1638, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403239332, "dur": 752, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403240092, "dur": 72498, "ph": "X", "name": "UnityBlacklistStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403312596, "dur": 5486, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403318086, "dur": 2498, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403320592, "dur": 136067, "ph": "X", "name": "DynamicDependencyLookupStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403456670, "dur": 385, "ph": "X", "name": "EarlyReportGenerationStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403457064, "dur": 684, "ph": "X", "name": "ResolveTestsStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403457759, "dur": 363, "ph": "X", "name": "LoadI18nAssemblies", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403458130, "dur": 1266, "ph": "X", "name": "ResolveMonoBehaviourItselfStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403459405, "dur": 616, "ph": "X", "name": "ResolveFromAllUserMonoBehaviours", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403460027, "dur": 4213, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403464243, "dur": 94, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403464338, "dur": 197, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403464536, "dur": 12, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403464549, "dur": 451, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403465001, "dur": 327, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403465329, "dur": 51, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403465381, "dur": 46, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403465427, "dur": 107, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403465534, "dur": 26, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403465560, "dur": 12, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403465573, "dur": 13, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403465587, "dur": 351, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403465938, "dur": 16, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403465955, "dur": 5, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403465960, "dur": 3, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403465964, "dur": 4, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403465969, "dur": 1, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403465971, "dur": 419, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403466391, "dur": 1, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403466397, "dur": 81468, "ph": "X", "name": "ResolveFromPreserveAttribute", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403547885, "dur": 13677, "ph": "X", "name": "EngineStrippingAnnotationStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403561577, "dur": 151325, "ph": "X", "name": "UnityTypeMapStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403712916, "dur": 267, "ph": "X", "name": "BeforeMarkReportGenerationStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403713190, "dur": 103439, "ph": "X", "name": "BeforeMarkAnalyticsStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403816643, "dur": 18420, "ph": "X", "name": "RemoveSecurityStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403835075, "dur": 9741, "ph": "X", "name": "RemoveSecurityFromCopyAssemblies", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403844832, "dur": 14740, "ph": "X", "name": "RemoveFeaturesStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403859587, "dur": 17473, "ph": "X", "name": "RemoveUnreachableBlocksStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478403877077, "dur": 3636645, "ph": "X", "name": "UnityMarkStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478407513735, "dur": 680, "ph": "X", "name": "ValidateVirtualMethodAnnotationsStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478407514422, "dur": 5202, "ph": "X", "name": "ProcessWarningsStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478407519632, "dur": 89809, "ph": "X", "name": "UnitySweepStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478407609452, "dur": 4934, "ph": "X", "name": "UnityCodeRewriterStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478407614397, "dur": 4638, "ph": "X", "name": "CleanStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478407619044, "dur": 2554, "ph": "X", "name": "StubifyStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478407621606, "dur": 2486, "ph": "X", "name": "AddUnresolvedStubsStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478407624097, "dur": 2931, "ph": "X", "name": "BeforeOutputAnalyticsStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478407627034, "dur": 137, "ph": "X", "name": "BeforeOutputReportGenerationStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478407627176, "dur": 404, "ph": "X", "name": "ProcessCopiedAssembliesRequiringSecurityAttributesRemoved", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478407627584, "dur": 439, "ph": "X", "name": "SealerStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478407628028, "dur": 1874506, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478409502550, "dur": 29594, "ph": "X", "name": "LinkerToEditorDataGenerationStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478409532158, "dur": 260, "ph": "X", "name": "ReportGenerationStep", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478409534516, "dur": 44068, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478409578843, "dur": 1294, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478409589650, "dur": 164, "ph": "X", "name": "", "args": {} },
{ "pid": 86996, "tid": 1, "ts": 1753478409586326, "dur": 3754, "ph": "X", "name": "Write chrome-trace events", "args": {} },
