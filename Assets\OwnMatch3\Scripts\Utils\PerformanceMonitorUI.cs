using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using System.Linq;

namespace OwnMatch3.Utils
{
    /// <summary>
    /// Performance monitoring UI for debugging frame drops and optimization effectiveness
    /// </summary>
    public class PerformanceMonitorUI : MonoBehaviour
    {
        [Header("UI References")]
        [SerializeField] private GameObject performancePanel;
        [SerializeField] private TextMeshProUGUI fpsText;
        [SerializeField] private TextMeshProUGUI profileText;
        [SerializeField] private TextMeshProUGUI animationCountText;
        [SerializeField] private TextMeshProUGUI memoryText;
        [SerializeField] private Button toggleButton;
        [SerializeField] private Slider maxAnimationsSlider;
        [SerializeField] private Slider batchDelaySlider;
        [SerializeField] private Toggle optimizationsToggle;
        
        [Header("Performance Tracking")]
        [SerializeField] private bool showPerformancePanel = false;
        [SerializeField] private float updateInterval = 0.5f;
        [SerializeField] private int fpsHistorySize = 60;
        
        private Match3Board board;
        private List<float> fpsHistory = new List<float>();
        private float lastUpdateTime;
        private bool isInitialized = false;
        
        // Performance thresholds
        private const float GOOD_FPS_THRESHOLD = 55f;
        private const float POOR_FPS_THRESHOLD = 25f;
        
        private void Start()
        {
            InitializeUI();
            FindBoard();
        }
        
        private void InitializeUI()
        {
            if (toggleButton != null)
            {
                toggleButton.onClick.AddListener(TogglePerformancePanel);
            }
            
            if (maxAnimationsSlider != null)
            {
                maxAnimationsSlider.onValueChanged.AddListener(OnMaxAnimationsChanged);
            }
            
            if (batchDelaySlider != null)
            {
                batchDelaySlider.onValueChanged.AddListener(OnBatchDelayChanged);
            }
            
            if (optimizationsToggle != null)
            {
                optimizationsToggle.onValueChanged.AddListener(OnOptimizationsToggled);
            }
            
            if (performancePanel != null)
            {
                performancePanel.SetActive(showPerformancePanel);
            }
            
            isInitialized = true;
        }
        
        private void FindBoard()
        {
            board = FindFirstObjectByType<Match3Board>();
            if (board == null)
            {
                DebugManager.LogMatchWarning("[PerformanceMonitorUI] Match3Board not found!");
                return;
            }
            
            // Initialize UI with current board settings
            UpdateUIFromBoard();
        }
        
        private void Update()
        {
            if (!isInitialized || board == null) return;
            
            if (Time.time - lastUpdateTime >= updateInterval)
            {
                UpdatePerformanceDisplay();
                lastUpdateTime = Time.time;
            }
        }
        
        private void UpdatePerformanceDisplay()
        {
            if (!showPerformancePanel) return;
            
            // Calculate current FPS
            float currentFPS = 1f / Time.unscaledDeltaTime;
            
            // Update FPS history
            fpsHistory.Add(currentFPS);
            if (fpsHistory.Count > fpsHistorySize)
            {
                fpsHistory.RemoveAt(0);
            }
            
            // Calculate average FPS
            float averageFPS = fpsHistory.Average();
            float minFPS = fpsHistory.Min();
            float maxFPS = fpsHistory.Max();
            
            // Update FPS display with color coding
            if (fpsText != null)
            {
                string fpsColor = GetFPSColor(currentFPS);
                fpsText.text = $"<color={fpsColor}>FPS: {currentFPS:F1}</color>\n" +
                              $"Avg: {averageFPS:F1} | Min: {minFPS:F1} | Max: {maxFPS:F1}";
            }
            
            // Update performance stats from board
            if (board != null)
            {
                var stats = board.GetPerformanceStats();
                
                if (profileText != null)
                {
                    profileText.text = $"Profile: {stats.profile}\nOptimizations: {(board.enablePerformanceOptimizations ? "ON" : "OFF")}";
                }
                
                if (animationCountText != null)
                {
                    animationCountText.text = $"Max Animations: {stats.animationCount}\nBatch Delay: {board.animationBatchDelay:F3}s";
                }
            }
            
            // Update memory usage
            if (memoryText != null)
            {
                long memoryUsage = System.GC.GetTotalMemory(false) / (1024 * 1024); // MB
                memoryText.text = $"Memory: {memoryUsage} MB\nGC Count: {System.GC.CollectionCount(0)}";
            }
        }
        
        private string GetFPSColor(float fps)
        {
            if (fps >= GOOD_FPS_THRESHOLD)
                return "green";
            else if (fps >= POOR_FPS_THRESHOLD)
                return "yellow";
            else
                return "red";
        }
        
        private void UpdateUIFromBoard()
        {
            if (board == null) return;
            
            if (maxAnimationsSlider != null)
            {
                maxAnimationsSlider.value = board.maxConcurrentAnimations;
            }
            
            if (batchDelaySlider != null)
            {
                batchDelaySlider.value = board.animationBatchDelay * 1000f; // Convert to milliseconds
            }
            
            if (optimizationsToggle != null)
            {
                optimizationsToggle.isOn = board.enablePerformanceOptimizations;
            }
        }
        
        private void TogglePerformancePanel()
        {
            showPerformancePanel = !showPerformancePanel;
            if (performancePanel != null)
            {
                performancePanel.SetActive(showPerformancePanel);
            }
        }
        
        private void OnMaxAnimationsChanged(float value)
        {
            if (board != null)
            {
                board.maxConcurrentAnimations = Mathf.RoundToInt(value);
            }
        }
        
        private void OnBatchDelayChanged(float value)
        {
            if (board != null)
            {
                board.animationBatchDelay = value / 1000f; // Convert from milliseconds
            }
        }
        
        private void OnOptimizationsToggled(bool enabled)
        {
            if (board != null)
            {
                board.SetPerformanceOptimizations(enabled);
            }
        }
        
        /// <summary>
        /// Set performance profile from UI
        /// </summary>
        public void SetPerformanceProfile(int profileIndex)
        {
            if (board != null)
            {
                var profile = (Match3Board.PerformanceProfile)profileIndex;
                board.SetPerformanceProfile(profile);
                UpdateUIFromBoard();
            }
        }
        
        /// <summary>
        /// Reset FPS history
        /// </summary>
        public void ResetFPSHistory()
        {
            fpsHistory.Clear();
        }
        
        /// <summary>
        /// Export performance data for analysis
        /// </summary>
        public void ExportPerformanceData()
        {
            if (fpsHistory.Count == 0) return;
            
            string data = "FPS History:\n";
            for (int i = 0; i < fpsHistory.Count; i++)
            {
                data += $"{i}: {fpsHistory[i]:F2}\n";
            }
            
            data += $"\nStatistics:\n";
            data += $"Average: {fpsHistory.Average():F2}\n";
            data += $"Min: {fpsHistory.Min():F2}\n";
            data += $"Max: {fpsHistory.Max():F2}\n";
            data += $"Std Dev: {CalculateStandardDeviation(fpsHistory):F2}\n";
            
            DebugManager.LogMatch($"[PerformanceMonitor] {data}");
        }
        
        private float CalculateStandardDeviation(List<float> values)
        {
            if (values.Count == 0) return 0f;
            
            float average = values.Average();
            float sumOfSquares = values.Sum(x => (x - average) * (x - average));
            return Mathf.Sqrt(sumOfSquares / values.Count);
        }
        
        /// <summary>
        /// Check if performance is currently poor
        /// </summary>
        public bool IsPerformancePoor()
        {
            if (fpsHistory.Count < 10) return false;
            
            float recentAverage = fpsHistory.TakeLast(10).Average();
            return recentAverage < POOR_FPS_THRESHOLD;
        }
        
        /// <summary>
        /// Get performance recommendation
        /// </summary>
        public string GetPerformanceRecommendation()
        {
            if (fpsHistory.Count < 30) return "Collecting performance data...";
            
            float averageFPS = fpsHistory.Average();
            
            if (averageFPS >= GOOD_FPS_THRESHOLD)
            {
                return "Performance is excellent! Consider enabling higher quality settings.";
            }
            else if (averageFPS >= POOR_FPS_THRESHOLD)
            {
                return "Performance is acceptable. Monitor for frame drops during complex animations.";
            }
            else
            {
                return "Performance is poor. Consider reducing animation quality or enabling optimizations.";
            }
        }
        
        private void OnDestroy()
        {
            if (toggleButton != null)
            {
                toggleButton.onClick.RemoveAllListeners();
            }
            
            if (maxAnimationsSlider != null)
            {
                maxAnimationsSlider.onValueChanged.RemoveAllListeners();
            }
            
            if (batchDelaySlider != null)
            {
                batchDelaySlider.onValueChanged.RemoveAllListeners();
            }
            
            if (optimizationsToggle != null)
            {
                optimizationsToggle.onValueChanged.RemoveAllListeners();
            }
        }
    }
}
